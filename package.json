{"name": "dmgcxt-web", "private": true, "version": "0.0.1", "type": "module", "workspaces": ["./packages/*"], "devDependencies": {"@commitlint/cli": "catalog:", "@commitlint/config-conventional": "catalog:", "@eslint/eslintrc": "catalog:", "@eslint/js": "catalog:", "@rollup/plugin-terser": "catalog:", "@types/node": "catalog:", "@types/nprogress": "catalog:", "@types/qs": "catalog:", "@typescript-eslint/eslint-plugin": "catalog:", "@typescript-eslint/parser": "catalog:", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "autoprefixer": "catalog:", "eslint": "catalog:", "eslint-config-prettier": "catalog:", "eslint-config-standard": "catalog:", "eslint-plugin-html": "catalog:", "eslint-plugin-import": "catalog:", "eslint-plugin-n": "catalog:", "eslint-plugin-prettier": "catalog:", "eslint-plugin-promise": "catalog:", "eslint-plugin-vue": "catalog:", "fast-glob": "catalog:", "globals": "catalog:", "less": "catalog:", "postcss": "catalog:", "prettier": "catalog:", "rollup-plugin-visualizer": "catalog:", "sass": "catalog:", "stylelint": "catalog:", "stylelint-config-standard": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:", "unplugin-auto-import": "catalog:", "unplugin-icons": "catalog:", "unplugin-vue-components": "catalog:", "unplugin-vue-define-options": "catalog:", "vite": "^6.3.5", "vite-plugin-compression": "catalog:", "vite-plugin-eslint": "catalog:", "vite-plugin-html": "catalog:", "vite-plugin-lazy-import": "catalog:", "vite-plugin-style-import": "catalog:", "vite-plugin-top-level-await": "catalog:", "vite-plugin-vue-setup-extend": "catalog:", "vite-svg-loader": "catalog:", "vue-tsc": "catalog:"}}