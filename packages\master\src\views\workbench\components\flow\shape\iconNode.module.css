.iconNode {
  position: relative;
  display: flex;
  background-color: #fff;
  border: 1px solid rgb(226 226 226 / 100%);
  border-radius: 10px;
  padding: 2px;
  width: 80px;
  height: 80px;
  align-items: center;
  box-shadow: 0 0 5px rgb(0 0 0 / 6%);
  color: #5361f3;
  text-align: center;
  box-sizing: border-box;
  transition: border 0.1s linear;
}

.iconNodeIcon {
  width: 60%;
  height: 60%;
  font-size: 32px;
  color: #5361f3;
  margin: auto;

  svg {
    vertical-align: text-top;
  }
}

.iconNode:empty:hover {
  border: 2px solid var(--primary-200);
}

.isDragOver {
  border: 2px solid var(--primary-200);
}

/* 添加节点选中时的高亮边框样式 */
.selected {
  border: 1px solid var(--primary-200);
  box-shadow: 0 0 8px rgba(83, 97, 243, 0.4);
}

.status {
  position: absolute;
  top: 3px;
  right: 8px;
  width: 16px;
  height: 16px;
}

.nodeName {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: -25px;
  text-align: center;
  font-weight: bold;
  color: var(--text-200);
  white-space: nowrap;
}
