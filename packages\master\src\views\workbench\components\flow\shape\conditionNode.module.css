.conditionNode {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: #fff;
  position: relative;
}

.conditionNodeHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #e8e8e8;
  cursor: move;
}

.transform {
  transform: rotate(90deg);
}
.nodeHeaderIcon {
  padding: 4px;
  cursor: pointer;
  transition: transform 0.3s;
}

.nodeName {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.businessNodeName {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: -25px;
  text-align: center;
  font-weight: bold;
  color: var(--text-200);
  white-space: nowrap;
}
.conditionContent {
  overflow: hidden;
  height: 100%;
}
.conditionNodeBody {
  padding: 8px;
}

.conditionItem {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  padding: 6px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  background-color: #f7f7f7;
}

.ifLabel {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
  padding: 0 4px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.conditionExpression {
  flex: 1;
  color: #333;
  font-size: 12px;
  word-break: break-all;
  overflow: hidden;
  margin-left: 8px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 添加节点选中时的高亮边框样式 */
.selected {
  border: 1px solid var(--primary-200);
  box-shadow: 0 0 8px rgba(83, 97, 243, 0.4);
}
