export default {
  placeholder: {
    label: '提示内容',
    value: '请输入',
    comp: 'a-input',
    compProps: {
      allowClear: true,
    },
  },
  label: {
    label: '标题',
    value: '标题',
    comp: 'a-input',
    compProps: {
      allowClear: true,
      placeholder: '请输入',
    },
  },
  value: {
    label: '值',
    value: undefined,
    comp: 'a-input',
    compProps: {
      allowClear: true,
      placeholder: '请输入',
    },
  },
  suffix: {
    label: '单位',
    value: undefined,
    comp: 'a-input',
    compProps: {
      allowClear: true,
      placeholder: '请输入',
    },
  },
} as Widget.PropsConfig;
