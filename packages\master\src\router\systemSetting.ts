import type { RouteRecordRaw } from 'vue-router';
import { RouterView } from 'vue-router';
import AntDesignSettingOutlined from '~icons/ant-design/setting-outlined';

const routes: RouteRecordRaw[] = [
  {
    path: 'system-setting',
    name: 'systemSetting',
    component: RouterView,
    meta: {
      title: '系统设置',
      icon: AntDesignSettingOutlined,
    },
    children: [
      {
        path: 'software',
        name: 'software',
        component: () =>
          import('@/master/views/systemSetting/software/index.vue'),
        meta: {
          title: '专业软件管理',
        },
      },
      {
        path: 'command',
        name: 'commands',
        component: () =>
          import('@/master/views/systemSetting/softwareCommand/index.vue'),
        meta: {
          title: '专业软件命令管理',
        },
      },
      {
        path: 'model',
        name: 'modelManagement',
        component: () => import('@/master/views/systemSetting/model/index.vue'),
        meta: {
          title: '模型管理',
        },
      },
      {
        path: 'running-monitor',
        name: 'operational-status',
        component: () =>
          import('@/master/views/systemSetting/runningMonitor/index.vue'),
        meta: {
          title: '运行状态监控',
        },
      },
      {
        path: 'workflow-instance',
        name: 'workflow-instance',
        component: () =>
          import('@/master/views/systemSetting/workflowInstance/index.vue'),
        meta: {
          title: '工作流实例',
        },
      },
      {
        path: 'workflow-instance-detail',
        name: 'workflow-instance-detail',
        component: () =>
          import('@/master/views/systemSetting/workflowInstance/Detail.vue'),
        meta: {
          showMenu: false,
        },
      },
    ],
  },
];
export default routes;
