import { Row, Col, RowProps } from 'ant-design-vue';

export default (
  props: RowProps & { column?: number } = {
    column: 3,
  }
) => {
  const slots = useSlots();
  const style = {
    height: `${props.style.height || 200}px`,
  };
  const cols = new Array(props.column).fill(null).map((_, i) => (
    <Col span={Math.floor(24 / props.column!)} class="h-full">
      {!slots.default && (
        <p
          class="text-center text-gray-300"
          style={{ lineHeight: style.height }}
        >
          拖动左侧元素到此处
        </p>
      )}
    </Col>
  ));
  return (
    <Row {...props} style={style}>
      {cols}
    </Row>
  );
};
