<!--
 * @file VueNode.vue
 * @description 树形节点组件，用于在数据关联模态框中显示参数树
-->

<template>
  <div class="custom-vue-node">
    <a-tree
      :default-expand-all="true"
      :tree-data="treeData"
      class="my-params com-tag"
      :expand-action="false"
      :expanded-keys="expandedKeys"
      :show-icon="true"
      :show-line="false"
      :field-names="{ children: 'children', title: 'name1', key: 'name' }"
    >
      <!-- 自定义展开/折叠图标 -->
      <template #switcherIcon="{ defaultIcon, isLeaf }">
        <component :is="defaultIcon" v-if="!isLeaf" />
      </template>

      <!-- 自定义树节点标题 -->
      <template #title="{ name1, label, type, isLeaf }">
        <div>
          <!-- 参数名称和类型图标 -->
          <span class="line-box" :title="name1">
            <component
              v-if="isLeaf"
              :is="getIcon(type === 'in' ? 'INPUT' : 'OUTPUT')"
              :class="type === 'in' ? 'text-blue-400' : 'text-orange-400'"
            />
            <span :style="{ fontWeight: isLeaf ? '' : 'bold' }">
              {{ label }}
            </span>
          </span>

          <!-- 参数值和单位 -->
          <!-- <span class="text-gray-400 ml-2"> {{ value }}{{ unit }} </span> -->
        </div>
      </template>
    </a-tree>
  </div>
</template>

<script setup lang="ts">
import { getIcon } from '@/master/views/toolbox/icons';
// 注入获取节点的方法
const getNode = inject('getNode') as any;

// 树形数据
const treeData = ref<any[]>();

// 已展开的节点键值
const expandedKeys = ref<string[]>([]);

/**
 * 获取所有节点的键值
 * @param data - 树形数据
 * @returns 所有节点的键值数组
 */
const getAllKeys = (data: any[]): string[] => {
  let keys: string[] = [];

  data.forEach((item: any) => {
    if (item.name) {
      keys.push(item.name);
    }

    if (item.children && item.children.length > 0) {
      keys = keys.concat(getAllKeys(item.children));
    }
  });

  return keys;
};

/**
 * 初始化数据
 */
const initData = (): void => {
  const node = getNode?.();
  if (!node) return;

  // 获取节点数据
  const data = node.getData();
  treeData.value = data.treeData;
  // 计算展开的节点键值
  if (treeData.value) {
    expandedKeys.value = getAllKeys(treeData.value);
  }

  // 监听节点数据变化
  node.on('change:data', ({ current }: { current: any }) => {
    if (current && current.treeData) {
      treeData.value = current.treeData;
      expandedKeys.value = getAllKeys(current.treeData);
    }
  });
};

// 组件挂载时初始化数据
onMounted(() => {
  initData();
});
</script>

<style scoped lang="less">
/**
 * 旋转图标样式
 */
.rotate90 {
  transform: rotate(-90deg);
}

/**
 * 自定义节点容器样式
 */
.custom-vue-node {
  width: 100%;
  border: 1px solid #ccc;
  box-shadow: 0 0 5px #ccc;
  border-radius: 8px;
  padding: 5px;
}

/**
 * 文本行样式，处理文本溢出
 */
.line-box {
  white-space: nowrap; /* 禁止文本换行 */
  overflow: hidden; /* 隐藏溢出的文本 */
  text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
}
</style>

<style>
/**
 * 标签样式
 */
.com-tag {
  border-radius: 4px;
  line-height: 14px !important;
  padding-inline: 3px !important;
  margin-inline-end: 2px !important;
}

/**
 * 树组件切换器样式
 */
.com-tree .ant-tree-switcher {
  width: 10px !important;
}

/**
 * 树节点无切换器样式
 */
.com-tree .ant-tree-list-holder .ant-tree-treenode .ant-tree-switcher-noop {
  width: 0px !important;
}

/**
 * 树节点内容包装器样式
 */
.com-tree .ant-tree-node-content-wrapper {
  padding: 0px;
}
</style>
