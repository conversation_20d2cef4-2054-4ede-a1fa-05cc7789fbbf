/**
 * 审批状态枚举
 */
export enum ApprovalStatus {
  /** 待审批 */
  PENDING = 'pending',
  /** 已通过 */
  APPROVED = 'approved',
  /** 已拒绝 */
  REJECTED = 'rejected',
  /** 已撤回 */
  WITHDRAWN = 'withdrawn',
}

/**
 * 审批活动类型枚举
 */
export enum ApprovalActivityType {
  /** 提交 */
  SUBMIT = 'submit',
  /** 通过 */
  APPROVE = 'approve',
  /** 拒绝 */
  REJECT = 'reject',
  /** 撤回 */
  WITHDRAW = 'withdraw',
}

/**
 * 审批统计数据接口
 */
export interface ApprovalStatistics {
  /** 待审批数量 */
  pending: number;
  /** 已通过数量 */
  approved: number;
  /** 已拒绝数量 */
  rejected: number;
  /** 我发起的数量 */
  initiated: number;
}

/**
 * 审批流程接口
 */
export interface ApprovalProcess {
  /** 审批ID */
  id: string;
  /** 流程名称 */
  name: string;
  /** 申请人 */
  applicant: string;
  /** 申请时间 */
  applyTime: string;
  /** 状态 */
  status: ApprovalStatus;
}

/**
 * 我发起的审批流程接口
 */
export interface InitiatedApprovalProcess extends ApprovalProcess {
  /** 当前审批人 */
  currentApprover: string;
}

/**
 * 审批活动接口
 */
export interface ApprovalActivity {
  /** 用户 */
  user: string;
  /** 动作 */
  action: string;
  /** 流程 */
  process: string;
  /** 时间 */
  time: string;
  /** 类型 */
  type: ApprovalActivityType;
}

/**
 * 审批趋势数据接口
 */
export interface ApprovalTrend {
  /** 日期 */
  dates: string[];
  /** 发起数 */
  initiated: number[];
  /** 完成数 */
  completed: number[];
}

/**
 * 审批详情接口
 */
export interface ApprovalDetail extends ApprovalProcess {
  /** 内容 */
  content: any;
  /** 审批历史 */
  approvalHistory: {
    /** 用户 */
    user: string;
    /** 动作 */
    action: string;
    /** 时间 */
    time: string;
    /** 评论 */
    comment: string;
  }[];
}

/**
 * 审批模板接口
 */
export interface ApprovalTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 模板图标 */
  icon: string;
}
