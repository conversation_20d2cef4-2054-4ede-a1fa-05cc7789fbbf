/**
 * @file constants.ts
 * @description Constants for the data association model
 */

// Node width constant
export const NODE_WIDTH = 360;

// Node height constant
export const NODE_HEIGHT = 50;

// Node spacing constant - used to calculate the spacing between nodes
export const NODE_SPACING = 28;

// Edge color constant
export const EDGE_COLOR = '#5F95FF';

// Node shape name
export const CUSTOM_NODE_SHAPE = 'custom-vue-node';

// Node IDs
export const NODE_IDS = {
  LEFT: 'vue-node-left',
  RIGHT: 'vue-node-right',
  GLOBAL: 'vue-node-global'
};

// Node positions
export const NODE_POSITIONS = {
  LEFT: { x: 10, y: 30 },
  RIGHT: { x: 440, y: 30 },
  GLOBAL: { x: 880, y: 30 }
};

// Port groups
export const PORT_GROUPS = {
  IN: 'in',
  OUT: 'out'
};
