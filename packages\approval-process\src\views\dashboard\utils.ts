import CheckOutlined from '~icons/ant-design/check-outlined';
import CloseOutlined from '~icons/ant-design/close-outlined';
import HistoryOutlined from '~icons/ant-design/history-outlined';
/**
 * 获取状态颜色
 * @param status 状态
 * @returns 颜色
 */
export function getStatusColor(status: string): string {
  const statusMap: Record<string, string> = {
    pending: 'blue',
    approved: 'green',
    rejected: 'red',
    withdrawn: 'orange',
  };
  return statusMap[status] || 'default';
}

/**
 * 获取状态文本
 * @param status 状态
 * @returns 文本
 */
export function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: '待审批',
    approved: '已通过',
    rejected: '已拒绝',
    withdrawn: '已撤回',
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取活动图标
 * @param type 活动类型
 * @returns 图标组件
 */
export function getActivityIcon(type: string): any {
  const iconMap: Record<string, any> = {
    submit: HistoryOutlined,
    approve: CheckOutlined,
    reject: CloseOutlined,
  };
  return iconMap[type] || HistoryOutlined;
}

/**
 * 获取活动颜色
 * @param type 活动类型
 * @returns 颜色
 */
export function getActivityColor(type: string): string {
  const colorMap: Record<string, string> = {
    submit: 'blue',
    approve: 'green',
    reject: 'red',
  };
  return colorMap[type] || 'blue';
}

/**
 * 获取待审批列表列定义
 * @returns 列定义
 */
export function getPendingColumns() {
  return [
    {
      title: '流程名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      key: 'applicant',
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      key: 'applyTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '操作',
      key: 'action',
    },
  ];
}
