# Vue 3 + TypeScript + Vite

### 开发

推荐包管理器使用 yarn 或 pnpm

```
    cd baseUI
    yarn / pnpm install
    yarn dev / pnpm run dev
```

### 目录结构

```
├── package.json
├── pnpm-lock.yaml
├── public
│   └── favicon.ico
├── router  // 路由配置
│   └── index.ts
├── src
│   ├── App.vue
│   ├── assets
│   │   └── logo.png
│   ├── components // 公共组件
│   │  
│   ├── env.d.ts
│   ├── main.ts    // 入口文件
│   ├── styles     // 样式文件
│   ├── types      // 类型定义
│   │   └── global.d.ts
│   └── views      // 视图页面
│       ├── Home.vue
│       └── Info.vue
|   |—— theme
        ├── normar.ts // 正常模式主题配置
        └── dark.ts // 暗黑模式主题配置
├── tsconfig.json   // ts配置
├── tsconfig.node.json
└── vite.config.ts  // vite配置
└── .prettierrc    // prettier配置
└── .eslintrc.js  // eslint配置
└── commitlint.config.js  // commitlint配置
```

### 编辑器设置

VS Code 应用市场推荐安装插件：

- `prettier`
- `eslint`
- `editorConfig for vscode`
- `Vue 3 Snippets Vue`
- `Language Features`
- `TypeScript Vue Plugin`
- `Tailwind CSS IntelliSense`
- `Tailwind Docs`

推荐配置：

```
    {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "eslint.validate": [
            "javascript",
            "javascriptreact",
            "html",
            "typescript"
        ],
         "css.lint.unknownAtRules": "ignore"
    }
```

### svg、iconify 图标本地打包

```
// 1. 配置create-bundle.js中sources变量下的icon和json，也就是在 iconify-set官网找到我们要打包的icon的名字填入，本地svg也可打包
// 2. 在终端中执行
node create-bundle.js
// 3. 代码引入
<iconify-icon name="打包时提供的图标名或custom:svg文件名" />
```

### 注意

windows 系统安装好 git 后，务必在命令行工具中运行以下命令关闭文件行尾换行符自动转换

```
git config --global core.autocrlf false
git config --global core.filemode false
git config --global core.safecrlf true
```

commitlint 检测提交格式：

```
git commit -m <type>[optional scope]: <description>
```

type ：用于表明我们这次提交的改动类型，是新增了功能？还是修改了测试代码？又或者是更新了文档？总结以下 11 种类型：

- build：主要目的是修改项目构建系统(例如 glup，webpack，rollup 的配置等)
- ci：主要目的是修改项目继续集成流程(例如 Travis，Jenkins，GitLab CI，Circle 等)的提交
- docs：文档更新
- feat：新增功能
- fix：bug 修复
- perf：性能优化
- refactor：重构代码(既没有新增功能，也没有修复 bug)
- style：不影响程序逻辑的代码修改(修改空白字符，补全缺失的分号等)
- test：新增测试用例或是更新现有测试
- revert：回滚某个更早之前的提交
- chore：不属于以上类型的其他类型(日常事务)
  optional scope：一个可选的修改范围。用于标识此次提交主要涉及哪个模块。
  description：一句话描述此次提交的主要内容，做到言简意赅
