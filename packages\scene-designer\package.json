{"name": "scene-designer", "version": "0.0.1", "type": "module", "description": "页面场景页面自定义设计", "main": "src/index.ts", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:staging": "vue-tsc --noEmit && vite build --mode staging", "build:stats": "vue-tsc --noEmit && vite build --mode stats", "preview": "vite preview", "format": "prittier . --write", "lint": "eslint --ext .js,.vue  --fix src"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "catalog:", "shared": "workspace:*", "vue3-colorpicker": "^2.3.0", "@iconify/json": "catalog:", "@iconify/vue": "catalog:", "@kd/hooks": "catalog:", "@kd/utils": "catalog:", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "core-js": "catalog:", "dayjs": "catalog:", "dialog-async": "catalog:", "nprogress": "catalog:", "pinia": "catalog:", "pinia-plugin-persistedstate": "catalog:", "qs": "catalog:", "query-string": "catalog:", "radash": "catalog:", "vue": "catalog:"}, "devDependencies": {}}