import { Form, FormItem, Input, Button } from 'ant-design-vue';
import { showDialog } from 'dialog-async';
import SelectModal from '../SelectModal';
import LetsIconsRemove from '~icons/lets-icons/remove';
import { TOptionsitem } from '@/scene-designer/types/common';
import { defineComponent, ref } from 'vue';
import type { PropType } from 'vue';

export default defineComponent({
  name: 'OptionsConfigModal',
  props: {
    value: Array as PropType<TOptionsitem[]>,
    options: Array as PropType<any[]>,
    modalWidth: {
      type: String,
      default: '30%',
    },
    inputWidth: {
      type: String,
      default: '400px',
    },
    validateDuplicate: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    emit('change', props.options);

    const list = ref<TOptionsitem[]>(
      JSON.parse(JSON.stringify(props.options)) || []
    );
    const activeId = ref();
    const inputVal = ref();

    const handleChange = (e: any, ind: number) => {
      activeId.value = ind;
      const val = (e.target as HTMLInputElement).value;
      inputVal.value = val;
    };

    const handleBlur = (e: any, ind: number) => {
      let val = (e.target as HTMLInputElement).value;

      if (props.validateDuplicate) {
        const isAlready = list.value.some(
          (item, i) => i !== ind && item.value === val
        );
        if (isAlready) val = val + '-1';
      }

      list.value = list.value.map((el: TOptionsitem, i: number) =>
        i === ind ? { value: val, label: val } : el
      );
    };

    const handleAdd = () => {
      const newIndex = list.value.length + 1;
      const item = {
        label: 'item' + newIndex,
        value: 'item' + newIndex,
      };
      list.value.push(item);
    };

    const handleRemove = (ind: number) => {
      list.value = list.value.filter((_, i) => i !== ind);
    };

    const submit = () => {
      emit('change', list.value);
    };

    const handleOpen = () => {
      showDialog(
        <SelectModal title="配置项" width={props.modalWidth} onSubmit={submit}>
          <Form labelCol={{ span: 4 }}>
            {list.value.map((f: any, ind: number) => (
              <FormItem
                key={f.value + ind}
                labelCol={{ span: 4 }}
                label={'选项' + (ind + 1)}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    cursor: 'pointer',
                  }}
                >
                  <Input
                    v-model:value={f.label}
                    placeholder={'请输入选项'}
                    style={{ width: props.inputWidth }}
                    onChange={e => handleChange(e, ind)}
                    onBlur={e => handleBlur(e, ind)}
                  />
                  {list.value.length > 1 && (
                    <LetsIconsRemove onClick={() => handleRemove(ind)} />
                  )}
                </div>
              </FormItem>
            ))}
            <FormItem>
              <div style={{ display: 'grid', placeItems: 'center' }}>
                <Button style={{ width: '200px' }} onClick={handleAdd}>
                  添加项
                </Button>
              </div>
            </FormItem>
          </Form>
        </SelectModal>
      );
    };

    return () => (
      <Button type={'primary'} onClick={handleOpen}>
        配置项
      </Button>
    );
  },
});
