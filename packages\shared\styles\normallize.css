body {
  margin: 0;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 14px;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #eff0f4;
}

#app {
  height: 100vh;
}

p {
  padding: 0;
  margin: 0;
}

ul,
li {
  list-style: none;
  padding: 0;
  margin: 0;
}

svg {
  display: inline-block;
  outline: none;
  vertical-align: -0.25em;
}

.anticon svg {
  vertical-align: baseline;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
}

fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

.table-striped td {
  background-color: #fafafa;
}
