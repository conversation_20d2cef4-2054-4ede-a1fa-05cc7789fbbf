<template>
  <a-card :title="title" class="col-span-2 h-[300px]">
    <template #extra>
      <a-button type="link" @click="$emit('viewMore')">查看更多</a-button>
    </template>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      size="small"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="$emit('approve', record as ApprovalProcess)"
            >
              审批
            </a-button>
            <a-button
              type="link"
              size="small"
              @click="$emit('viewDetail', record as ApprovalProcess)"
            >
              详情
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </a-card>
</template>

<script setup lang="ts">
import { getStatusColor, getStatusText } from '../utils';
import type { ApprovalProcess } from '@/approval-process/types/approval';

defineProps<{
  title: string;
  dataSource: ApprovalProcess[];
  columns: any[];
}>();

defineEmits<{
  (e: 'viewMore'): void;
  (e: 'approve', record: ApprovalProcess): void;
  (e: 'viewDetail', record: ApprovalProcess): void;
}>();
</script>
