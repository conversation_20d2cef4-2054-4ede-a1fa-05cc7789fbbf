---
description: 
globs: 
alwaysApply: false
---

      You have extensive expertise in Vue 3, TypeScript, Node.js, Vite, Vue Router, Pinia, VueUse, Ant Design Vue, and Tailwind CSS. You possess a deep knowledge of best practices and performance optimization techniques across these technologies.

      Code Style and Structure
      - Write clean, maintainable, and technically accurate TypeScript code.
      - Prioritize functional and declarative programming patterns; avoid using classes.
      - Emphasize iteration and modularization to follow DRY principles and minimize code duplication.
      - Prefer Composition API <script setup> style.
      - Use Composables to encapsulate and share reusable client-side logic or state across multiple components in your Nuxt application.
      - Use functional, declarative programming patterns.
      - Prefer iteration and modularization over code duplication. 
      - Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
      - Organize files systematically: each file should contain only related content, such as exported components, subcomponents, helpers, static content, and types.
      - Favor named exports for functions.
      - Use the "function" keyword for pure functions to benefit from hoisting and clarity.
      - Prefer the Receive an Object, Return an Object (RORO) pattern for function parameters. 
      - Prefer one-line syntax for simple conditional statements (e.g., if (condition) doSomething()). 
      - Error Handling and Validation - Handle errors and edge cases at the beginning of functions. 
      - Use early returns for error conditions to avoid deeply nested if statements. 
      - Use guard clauses to handle preconditions and invalid states early.
      - Avoid unnecessary else statements; use if-return pattern instead.
      - Implement proper error logging and user-friendly error messages.
      - Consider using custom error types or error factories for consistent error handling.
      - Leverage VueUse functions where applicable to enhance reactivity and performance.
      - Prefer the "function" keyword for methods but use arrow functions with const for computed properties.
      - Prefer the `defineModel` macro for creating two-way bindings.
      - Use the succint syntax for defineEmits (e.g. `change: [id: number]`)
      - Implement responsive design with Tailwind CSS; use a mobile-first approach.
      - Performance Optimization
      - Wrap asynchronous components in Suspense with a fallback UI.
      - Use dynamic loading for non-critical components.
      - Implement an optimized chunking strategy during the Vite build process, such as code splitting, to generate smaller bundle sizes.

      Naming Conventions
      - Utilize composables, naming them as use<MyComposable>.
      - Use **PascalCase** for component file names (e.g., components/MyComponent.vue).
      - Favor named exports for functions to maintain consistency and readability.

      TypeScript Usage
      - Use TypeScript throughout; prefer interfaces over types for better extendability and merging.
      - Avoid enums, opting for maps for improved type safety and flexibility.
      - Use functional components with TypeScript interfaces.

      UI and Styling
      - Use Ant Design Vue and Tailwind CSS for components and styling.
      - Implement responsive design with Tailwind CSS; use a mobile-first approach.

      






