/**
 * @file registerNodes.ts
 * @description 注册审批流程自定义节点
 */

import { register } from '@antv/x6-vue-shape';
import ApprovalNode from '../components/ApprovalNode';
import ConditionNode from '../components/AddConditionButton.vue';

/**
 * 注册审批流程自定义节点
 */
export function registerApprovalNodes(): void {
  // 连接桩基本属性
  const baseAttrs = {
    circle: {
      r: 1, // 半径
      magnet: true, // 可作为连接点
      stroke: '#40CFA0', // 描边颜色
      strokeWidth: 1, // 描边宽度
      fill: '#fff', // 填充颜色
      style: { visibility: 'hidden' }, // 初始隐藏
    },
  };

  // 注册审批节点
  register({
    shape: 'approval-node', // 节点类型名称
    component: ApprovalNode, // 节点组件
    ports: {
      groups: {
        // 上方连接桩组
        top: {
          position: 'top',
          attrs: baseAttrs,
        },
        // 下方连接桩组
        bottom: {
          position: 'bottom',
          attrs: baseAttrs,
        },
      },
      // 默认添加的连接桩
      items: [
        { group: 'top', id: 'top' },
        { group: 'bottom', id: 'bottom' },
      ],
    },
  });

  // 注册条件节点
  register({
    shape: 'condition-node', // 节点类型名称
    component: ConditionNode, // 节点组件
    ports: {
      groups: {
        // 上方连接桩组
        top: {
          position: 'top',
          attrs: baseAttrs,
        },
        // 下方连接桩组（每个条件对应一个）
        bottom: {
          position: 'bottom',
          attrs: baseAttrs,
        },
        // 左侧连接桩组
        left: {
          position: 'left',
          attrs: baseAttrs,
        },
        // 右侧连接桩组
        right: {
          position: 'right',
          attrs: baseAttrs,
        },
      },
      // 默认添加的连接桩
      items: [
        { group: 'top', id: 'top' },
        { group: 'left', id: 'left' },
        { group: 'right', id: 'right' },
        // { group: 'bottom', id: 'bottom' },
      ],
    },
  });
}
