import { CheckboxGroup, CheckboxGroupProps } from 'ant-design-vue';
export default ({
  label,
  style,
  list,
  ...props
}: CheckboxGroupProps & {
  label: string;
  style?: Record<string, string>;
  list:
    | string[]
    | number[]
    | Array<{ label: string; value: string; disabled?: boolean }>;
}) => {
  return (
    <div
      style={{
        display: 'inline-flex',
        alignItems: 'center',
      }}
    >
      {label && (
        <label style="white-space: nowrap;padding:0 8px;">{label}:</label>
      )}
      <CheckboxGroup {...props} options={list} />
    </div>
  );
};
