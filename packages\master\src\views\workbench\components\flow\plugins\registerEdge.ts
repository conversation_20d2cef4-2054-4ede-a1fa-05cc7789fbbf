/**
 * @file registerEdge.ts
 * @description 注册自定义边和边工具
 */

import { Graph } from '@antv/x6';

/**
 * 注册自定义边
 * @returns 注册结果
 */
export function registerEdge(): any {
  return Graph.registerEdge(
    'dashed-edge', // 边类型名称
    {
      inherit: 'edge', // 继承自基础边
      // 边的 SVG 标记
      markup: [
        {
          tagName: 'path',
          selector: 'wrap',
          attrs: {
            fill: 'none',
            cursor: 'pointer',
            stroke: 'transparent',
            strokeLinecap: 'round',
          },
        },
        {
          tagName: 'path',
          selector: 'line',
          attrs: {
            fill: 'none',
            pointerEvents: 'none',
          },
        },
      ],
      connector: { name: 'rounded' }, // 连接器类型
      // 边的属性
      attrs: {
        wrap: {
          connection: true,
          strokeWidth: 10,
          strokeLinejoin: 'round',
        },
        line: {
          connection: true,
          stroke: '#A2B1C3', // 边的颜色
          strokeWidth: 2, // 边的宽度
          targetMarker: {
            name: 'block', // 箭头类型
            size: 6, // 箭头大小
          },
        },
      },
      zIndex: 20, // 层级，确保边在节点上方
    },
    true // 覆盖现有定义
  );
}

/**
 * 注册边工具
 */
export function registerEdgeTool(): void {
  // 注册加号按钮工具
  Graph.registerEdgeTool(
    'plus-button',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'circle',
          selector: 'button',
          attrs: {
            r: 12,
            stroke: '#2196F3',
            'stroke-width': 1,
            fill: 'white',
            cursor: 'pointer',
          },
        },
        {
          tagName: 'path',
          selector: 'icon',
          attrs: {
            d: 'M19 1h-6v6h-2v-6H5v-2h6v-6h2v6h6z',
            fill: '#999',
            stroke: '#999',
            'stroke-width': 0.5,
            style: 'transform:translateX(-12px)',
          },
        },
      ],
      useCellGeometry: true,
      offset: 0,
      distance: '50%',
      onClick: ({ e, view }: any) => {
        const graph = view.graph;
        if (graph && typeof graph.addEmptyNode === 'function') {
          graph.addEmptyNode(e, view);
        }
      },
    },
    true
  );
}
