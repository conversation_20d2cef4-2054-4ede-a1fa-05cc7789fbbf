---
description: 
globs: 
alwaysApply: false
---
# Cursor MDC Configuration File
# Description: This configuration file guides the AI tool to generate functional testing code
# for projects using Vue 3, Vite, and TypeScript.

config:
  # Project Technology Stack
  tech_stack:
    framework: "vue" # Framework (e.g., vue, react)
    version: "3.x" # Vue version
    build_tool: "vite" # Build tool (e.g., vite, webpack)
    language: "typescript" # Programming language (e.g., javascript, typescript)

  # Test Framework Configuration
  test_framework:
    name: "vitest" # Testing framework (e.g., jest, vitest, cypress)
    setup_command: "npm install --save-dev vitest @vue/test-utils@next @testing-library/vue @testing-library/jest-dom" # Installation command
    version: "latest" # Use the latest version of the framework
    globals: true # Enable global test utilities (e.g., describe, it)

  # Test Environment
  test_environment:
    type: "jsdom" # Test environment (e.g., node, jsdom for browser-like environment)
    config:
      globals: true # Enable global variables in the test environment

  # Test Coverage Requirements
  test_coverage:
    enabled: true # Enable test coverage
    thresholds:
      lines: 80 # Minimum line coverage percentage
      functions: 80 # Minimum function coverage percentage
      branches: 70 # Minimum branch coverage percentage
      statements: 80 # Minimum statement coverage percentage
    report: true # Generate a coverage report
    coverage_tool: "vitest" # Coverage tool (e.g., vitest, istanbul)

  # Test Structure
  test_structure:
    setup_hooks:
      - "import { describe, it, expect, beforeEach, afterEach } from 'vitest';"
      - "import { mount } from '@vue/test-utils';"
    describe_blocks:
      description: "Group tests by feature/module"
      example: "describe('Button Component', () => { ... })"
    it_blocks:
      description: "Group tests by functionality"
      example: "it('renders the button with correct text', () => { ... })"
    async_handling:
      description: "Support for async/await testing"
      example: "test('async function', async () => { ... })"

  # Naming Conventions
  naming_conventions:
    test_file_suffix: ".spec.ts" # Suffix for test files (e.g., .spec.ts, .test.ts)
    describe_name: "Feature or Module Name" # Name of describe blocks
    it_name: "Specific Functionality Description" # Name of it blocks
    function_prefix: "" # No prefix for test functions (optional)

  # Mocking Strategy
  mocking_strategy:
    external_api_calls: true # Mock external API calls
    dependencies: true # Mock dependencies (e.g., Vuex, Pinia, router)
    mock_tool: "vitest.fn" # Mocking tool (e.g., vitest.fn, jest.mock)

  # Async Testing
  async_testing:
    enabled: true # Enable async function testing
    tools: ["async/await", "promises"] # Supported async testing tools

  # Error Handling
  error_handling:
    enabled: true # Test error handling logic
    example: "expect(() => { ... }).toThrow('Error message')"

  # Test Data Management
  test_data_management:
    use_fixtures: true # Use fixed test data
    use_factories: false # Use factory functions to generate test data
    data_generator_tool: "" # No specific data generator tool for now

  # Code Style and Formatting
  code_style:
    formatting_tool: "prettier" # Code formatting tool
    linting_tool: "eslint" # Code linting tool
    import_order: ["vue", "@vue/test-utils", "vitest", "src/*"] # Import order configuration
    max_line_length: 100 # Maximum line length
    tsconfig_path: "tsconfig.json" # Path to TypeScript configuration file

  # CI/CD Integration
  ci_integration:
    enabled: true # Enable CI/CD integration
    tools:
      - "github-actions"
      - "circleci"
    commands:
      - "npm run test" # Command to run tests
      - "npm run coverage" # Command to generate coverage report

  # Test Reporting
  test_report:
    generate_report: true # Generate a test report
    report_format: "html" # Report format (e.g., html, json, text)
    report_tool: "vitest" # Tool for generating test reports

  # Additional Tools and Plugins
  additional_tools:
    snapshot_testing: true # Enable snapshot testing
    accessibility_testing: false # Enable accessibility testing (e.g., axe-core)
    coverage_visualization: "vitest" # Coverage visualization tool

  # Vue-Specific Testing Configuration
  vue_testing:
    component_mounting:
      description: "Use @vue/test-utils for mounting components"
      example: |
        import { mount } from '@vue/test-utils';
        const wrapper = mount(MyComponent);
    props_testing:
      description: "Test component props"
      example: |
        expect(wrapper.props('propName')).toBe(expectedValue);
    emits_testing:
      description: "Test emitted events"
      example: |
        expect(wrapper.emitted('eventName')).toBeTruthy();
    state_management:
      description: "Mock state management libraries (e.g., Pinia, Vuex)"
      example: |
        jest.mock('pinia', () => ({
          createPinia: () => ({
            useStore: () => ({ state: { ... } }),
          }),
        }));

# Example Test File Structure
example_test_file:
  path: "src/components/Button/Button.spec.ts"
  content: |
    import { describe, it, expect } from 'vitest';
    import { mount } from '@vue/test-utils';
    import Button from './Button.vue';

    describe('Button Component', () => {
      it('renders the button with correct text', () => {
        const wrapper = mount(Button, {
          props: {
            label: 'Click Me',
          },
        });
        expect(wrapper.text()).toContain('Click Me');
      });

      it('triggers onClick handler when clicked', async () => {
        const handleClick = jest.fn();
        const wrapper = mount(Button, {
          props: {
            onClick: handleClick,
          },
        });
        await wrapper.trigger('click');
        expect(handleClick).toHaveBeenCalledTimes(1);
      });
    });