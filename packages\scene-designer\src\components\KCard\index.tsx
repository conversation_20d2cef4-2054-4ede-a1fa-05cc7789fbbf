import { Card } from 'ant-design-vue';
import <PERSON><PERSON>r from '../KContainer';
import Wrapper from '@/scene-designer/views/workbench/dashboard/Wrapper.vue';

export default defineComponent({
  name: '<PERSON><PERSON>',
  props: {
    title: {
      type: String,
      default: '标题',
    },
    id: String,
  },
  setup(props) {
    const slots = useSlots();
    return () => (
      <Card title={props.title} style="margin-top: 10px">
        <Wrapper>
          {slots.default ? (
            <KContainer id={props.id}>{slots.default()}</KContainer>
          ) : (
            <KContainer id={props.id} />
          )}
        </Wrapper>
      </Card>
    );
  },
});
