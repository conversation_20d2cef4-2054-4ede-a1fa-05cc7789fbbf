import type { ApprovalStatistics, ApprovalTrend } from '@/approval-process/types/approval';

/**
 * 获取饼图配置
 * @param statistics 统计数据
 * @returns 饼图配置
 */
export function getPieOptions(statistics: ApprovalStatistics) {
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['待审批', '已通过', '已拒绝', '已撤回'],
    },
    series: [
      {
        name: '审批状态',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: statistics.pending, name: '待审批', itemStyle: { color: '#1890ff' } },
          { value: statistics.approved, name: '已通过', itemStyle: { color: '#52c41a' } },
          { value: statistics.rejected, name: '已拒绝', itemStyle: { color: '#f5222d' } },
          { value: 3, name: '已撤回', itemStyle: { color: '#faad14' } },
        ],
      },
    ],
  };
}

/**
 * 获取折线图配置
 * @param trend 趋势数据
 * @returns 折线图配置
 */
export function getLineOptions(trend: ApprovalTrend) {
  const dates = trend.dates.length > 0 
    ? trend.dates 
    : ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  
  const initiated = trend.initiated.length > 0 
    ? trend.initiated 
    : [5, 7, 3, 9, 12, 6, 8];
  
  const completed = trend.completed.length > 0 
    ? trend.completed 
    : [3, 5, 2, 8, 10, 5, 7];

  return {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['发起数', '完成数'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '发起数',
        type: 'line',
        data: initiated,
        smooth: true,
        itemStyle: {
          color: '#1890ff',
        },
      },
      {
        name: '完成数',
        type: 'line',
        data: completed,
        smooth: true,
        itemStyle: {
          color: '#52c41a',
        },
      },
    ],
  };
}
