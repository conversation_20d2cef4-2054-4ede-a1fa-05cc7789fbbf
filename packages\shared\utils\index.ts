import dayjs from 'dayjs';

/**
 * 格式化日期为 YYYY-MM-DD
 * @param date 日期对象、字符串或数字
 * @returns 格式化后的日期字符串或undefined
 */
export const formatDate = (
  date?: Date | string | number
): string | undefined => (date ? dayjs(date).format('YYYY-MM-DD') : undefined);

/**
 * 格式化日期时间为 YYYY-MM-DD HH:mm:ss
 * @param date 日期对象、字符串或数字
 * @returns 格式化后的日期时间字符串或undefined
 */
export const formatDateTime = (
  date?: Date | string | number
): string | undefined =>
  date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : undefined;

/**
 * 生成UUID
 * 优先使用浏览器原生的UUID生成方法，如果不支持则使用备选方案
 * @returns UUID字符串
 */
export const generateUUID = (): string => {
  // 使用浏览器原生的UUID生成方法（如果支持）
  if (typeof crypto === 'object' && typeof crypto.randomUUID === 'function') {
    return crypto.randomUUID();
  }

  // 使用加密API生成UUID
  if (
    typeof crypto === 'object' &&
    typeof crypto.getRandomValues === 'function' &&
    typeof Uint8Array === 'function'
  ) {
    return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, c => {
      const num = Number(c);
      return (
        num ^
        (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))
      ).toString(16);
    });
  }

  // 如果上述方法都不支持，使用时间戳和随机数生成UUID
  const timestamp = new Date().getTime();
  const performanceTime =
    typeof performance !== 'undefined' && typeof performance.now === 'function'
      ? performance.now() * 1000
      : 0;

  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    // 使用时间戳和随机数生成随机值
    let r = Math.random() * 16;
    const time = timestamp > 0 ? timestamp : performanceTime;
    r = (time + r) % 16 | 0;

    // 对于'x'使用随机值，对于'y'使用特定的位操作以符合UUID规范
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
};

export const isObjectString = (str: string) => str.startsWith('{');
