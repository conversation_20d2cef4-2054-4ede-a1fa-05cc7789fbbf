/**
 * @file registerNode.ts
 * @description 注册自定义节点
 */

import { register } from '@antv/x6-vue-shape';
import IconNode from '../shape/IconNode';
import GroupNode from '../shape/GroupNode';
import ConditionNode from '../shape/ConditionNode';

/**
 * 注册自定义节点
 */
export function registerNode(): void {
  // 连接桩基本属性
  const baseAttrs = {
    circle: {
      r: 6, // 半径
      magnet: true, // 可作为连接点
      stroke: '#31d0c6', // 描边颜色
      strokeWidth: 1, // 描边宽度
      fill: '#fff', // 填充颜色
      style: { visibility: 'hidden' }, // 初始隐藏
    },
  };

  // 注册图标节点
  register({
    shape: 'IconNode', // 节点类型名称
    component: IconNode, // 节点组件
    ports: {
      groups: {
        // 上方连接桩组
        top: {
          position: 'top',
          attrs: baseAttrs,
        },
        // 下方连接桩组
        bottom: {
          position: 'bottom',
          attrs: baseAttrs,
        },
        // 左侧连接桩组
        left: {
          position: 'left',
          attrs: baseAttrs,
        },
        // 右侧连接桩组
        right: {
          position: 'right',
          attrs: baseAttrs,
        },
      },
      // 默认添加的连接桩
      items: [
        { group: 'top' },
        { group: 'right' },
        { group: 'bottom' },
        { group: 'left' },
      ],
    },
  });

  // 注册分组节点
  register({
    shape: 'GroupNode', // 节点类型名称
    component: GroupNode, // 节点组件
    ports: {
      groups: {
        // 上方连接桩组
        top: {
          position: 'top',
          attrs: baseAttrs,
        },
        // 下方连接桩组
        bottom: {
          position: 'bottom',
          attrs: baseAttrs,
        },
        // 左侧连接桩组
        left: {
          position: 'left',
          attrs: baseAttrs,
        },
        // 右侧连接桩组
        right: {
          position: 'right',
          attrs: baseAttrs,
        },
      },
      // 默认添加的连接桩
      items: [
        { group: 'top' },
        { group: 'right' },
        { group: 'bottom' },
        { group: 'left' },
      ],
    },
  });

  // 注册条件判断节点
  register({
    shape: 'ConditionNode', // 节点类型名称
    component: ConditionNode, // 节点组件
    ports: {
      groups: {
        // 左侧连接桩组（入口）
        left: {
          position: 'left',
          attrs: baseAttrs,
        },
        // 右侧连接桩组（出口，每个条件对应一个）
        right: {
          position: 'absolute',
          attrs: baseAttrs,
        },
      },
      // 默认添加的连接桩，只添加左侧入口连接桩，右侧连接桩将由组件动态管理
      items: [
        { group: 'left', id: 'condition-input' }, // 左侧入口连接桩
      ],
    },
  });
}
