/**
 * 模型版本控制表接口定义
 */
export interface SysDesModelAttachment extends Global.Entity {
  /** 主键 */
  sysDesModelAttachmentId: string;
  /** 模型版本 */
  modelVersion: string;
  /** 模型名称 */
  modelName: string;
  /** 模型路径 */
  modelPath: string;
  /** 模型ID */
  sysDesModelId: string;
  /** 模型截图 */
  modelImage: string;
  /** 备注 */
  remark: string;
  /** 1-默认，0-非默认 */
  defaultFlag: string;
  /** 文件夹上传，文件列表 */
  fileListPath: string | null;
}

/**
 * 模型版本控制表查询参数
 */
export interface SysDesModelAttachmentQueryParams {
  /** 主键 */
  sysDesModelAttachmentId?: string;
  /** 模型版本 */
  modelVersion?: string;
  /** 模型名称 */
  modelName?: string;
  /** 模型路径 */
  modelPath?: string;
  /** 模型ID */
  sysDesModelId?: string;
  /** 模型截图 */
  modelImage?: string;
  /** 备注 */
  remark?: string;
  /** 1-默认，0-非默认 */
  defaultFlag?: string;
  /** 文件夹上传，文件列表 */
  fileListPath?: string | null;
}

/**
 * 模型存储服务器信息接口定义
 */
export interface SysDesModelServer extends Global.Entity {
  /** 主键ID */
  sysDesModelServerId: string;
  /** 专业软件主键ID */
  desProfessionalSoftwareId: string;
  /** endpoint */
  endPoint: string;
  /** accessKey */
  accessKey: string;
  /** secretKey */
  secretKey: string;
  /** bucketName */
  bucketName: string;
  /** saveBasePath */
  saveBasePath: string;
  /** 备注 */
  remark: string;
}

/**
 * 模型存储服务器信息查询参数
 */
export interface SysDesModelServerQueryParams {
  /** 主键ID */
  sysDesModelServerId?: string;
  /** 专业软件主键ID */
  desProfessionalSoftwareId?: string;
  /** endpoint */
  endPoint?: string;
  /** accessKey */
  accessKey?: string;
  /** secretKey */
  secretKey?: string;
  /** bucketName */
  bucketName?: string;
  /** saveBasePath */
  saveBasePath?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 上传模型图片响应
 */
export interface UploadModelImageResponse {
  base64: string;
}
