<template>
  <a-config-provider
    :locale="zhCN"
    :theme="{
      token: {
        colorPrimary: '#5362f2',
      },
      components: {
        Table: {
          padding: 8,
          paddingContentVerticalLG: 8,
        },
        Card: {
          paddingLG: 12,
        },
      },
    }"
  >
    <DialogProvider>
      <style-provider hash-priority="high">
        <router-view />
      </style-provider>
    </DialogProvider>
  </a-config-provider>
</template>
<script setup lang="ts">
import { StyleProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { DialogProvider } from 'dialog-async';
</script>
