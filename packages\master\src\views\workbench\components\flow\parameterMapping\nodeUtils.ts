/**
 * @file nodeUtils.ts
 * @description Utility functions for node creation and configuration
 */

import { Graph, Node } from '@antv/x6';
import {
  NODE_WIDTH,
  NODE_SPACING,
  CUSTOM_NODE_SHAPE,
  NODE_IDS,
  NODE_POSITIONS,
  PORT_GROUPS,
} from './constants';
import { TreeNode, GlobalParam, PortCustomData } from './types';

/**
 * Creates a port configuration object
 * @param group - The port group ('in' or 'out')
 * @returns The port configuration object
 */
function createPortConfig(group: string) {
  return {
    position: {
      name: 'absolute',
      args: {
        x: 0,
        y: 0,
      },
    },
    attrs: {
      circle: {
        r: 6,
        magnet: true,
        stroke: '#5F95FF',
        strokeWidth: 1,
        fill: '#fff',
      },
    },
  };
}

/**
 * Creates a node with the specified configuration
 * @param graph - The graph instance
 * @param id - The node ID
 * @param x - The x position
 * @param y - The y position
 * @param title - The node title
 * @param treeData - The tree data for the node
 * @param portGroups - The port groups to include
 * @returns The created node
 */
export function createNode(
  graph: Graph,
  id: string,
  x: number,
  y: number,
  title: string,
  treeData: TreeNode[],
  portGroups: string[] = [PORT_GROUPS.IN]
): Node {
  const portGroupsConfig: Record<string, any> = {};

  portGroups.forEach(group => {
    portGroupsConfig[group] = createPortConfig(group);
  });

  return graph.addNode({
    id,
    shape: CUSTOM_NODE_SHAPE,
    x,
    y,
    data: {
      title,
      treeData,
    },
    ports: {
      groups: portGroupsConfig,
    },
  });
}

/**
 * Adds ports to a node based on tree data
 * @param node - The node to add ports to
 * @param treeData - The tree data containing port information
 * @param portGroup - The port group to add ports to
 * @param xPosition - The x position for the ports
 * @param isLeftSide - Whether the ports are on the left side
 */
export function addPortsFromTreeData(
  node: Node,
  treeData: TreeNode[],
  portGroup: string,
  xPosition: number,
  isLeftSide: boolean = true
): void {
  let index = 0;

  for (let i = 0; i < treeData.length; i++) {
    const row = treeData[i].children;
    index += 1;

    for (let j = 0; j < row.length; j++) {
      index += 1;
      const col = row[j];

      node.addPort({
        id: `${isLeftSide ? 'left' : 'right'}-${index}`,
        group: portGroup,
        args: {
          x: xPosition,
          y: index * NODE_SPACING - 9,
        },
        customData: {
          id: col.name,
          name: col.label,
          value: col.value,
          unit: col.unit,
          type: col.type,
          nodeId: col.nodeId,
        } as PortCustomData,
      });
    }
  }
}

/**
 * Creates the left node (current node)
 * @param graph - The graph instance
 * @param treeData - The tree data
 * @returns The created node
 */
export function createLeftNode(graph: Graph, treeData: TreeNode[]): Node {
  const node = createNode(
    graph,
    NODE_IDS.LEFT,
    NODE_POSITIONS.LEFT.x,
    NODE_POSITIONS.LEFT.y,
    '当前节点',
    treeData
  );

  addPortsFromTreeData(node, treeData, PORT_GROUPS.IN, NODE_WIDTH + 10);

  return node;
}

/**
 * Creates the right node (all nodes)
 * @param graph - The graph instance
 * @param treeData - The tree data
 * @returns The created node
 */
export function createRightNode(graph: Graph, treeData: TreeNode[]): Node {
  const node = createNode(
    graph,
    NODE_IDS.RIGHT,
    NODE_POSITIONS.RIGHT.x,
    NODE_POSITIONS.RIGHT.y,
    '所有节点',
    treeData,
    [PORT_GROUPS.IN, PORT_GROUPS.OUT]
  );

  let index = 0;

  for (let i = 0; i < treeData.length; i++) {
    const row = treeData[i].children;
    index += 1;

    for (let j = 0; j < row.length; j++) {
      index += 1;
      const col = row[j];

      // Add output port (left side)
      node.addPort({
        id: `right-left${index}`,
        group: PORT_GROUPS.OUT,
        args: {
          x: 0,
          y: index * NODE_SPACING - 9,
        },
        customData: {
          id: col.name,
          name: col.label,
          value: col.value,
          unit: col.unit,
          type: col.type,
          nodeId: col.nodeId,
        } as PortCustomData,
      });

      // Add input port (right side)
      node.addPort({
        id: `right-right${index}`,
        group: PORT_GROUPS.IN,
        args: {
          x: NODE_WIDTH + 10,
          y: index * NODE_SPACING - 9,
        },
        customData: {
          id: col.name,
          name: col.label,
          value: col.value,
          unit: col.unit,
          type: col.type,
          nodeId: col.nodeId,
        } as PortCustomData,
      });
    }
  }

  return node;
}

/**
 * Creates the global node (global parameters)
 * @param graph - The graph instance
 * @param globalData - The global parameter data
 * @returns The created node or null if no global data
 */
export function createGlobalNode(
  graph: Graph,
  globalData: GlobalParam[]
): Node | null {
  const globalDataArr = [
    {
      componentname: '全局参数',
      componenttype: 'all',
      name: 'global',
      label: '全局参数',
      sysdescomponentid: '',
      children: globalData.map(g => ({
        name: g.paramKey,
        label: g.paramKey,
        type: 'out',
        unit: '',
        value: g.paramValue,
      })),
    },
  ];

  const node = createNode(
    graph,
    NODE_IDS.GLOBAL,
    NODE_POSITIONS.GLOBAL.x,
    NODE_POSITIONS.GLOBAL.y,
    '全局参数',
    globalDataArr,
    [PORT_GROUPS.OUT]
  );

  for (let i = 0; i < globalDataArr[0].children.length; i++) {
    const item = globalDataArr[0].children[i];

    node.addPort({
      id: `global-${i}`,
      group: PORT_GROUPS.OUT,
      args: {
        x: 0,
        y: (i + 2) * NODE_SPACING - 9,
      },
      customData: {
        id: item.name,
        name: item.label,
        value: item.value,
        unit: item.unit,
        type: item.type,
      } as PortCustomData,
    });
  }

  return node;
}
