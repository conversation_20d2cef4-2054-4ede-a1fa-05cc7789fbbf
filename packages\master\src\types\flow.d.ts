import type { Tool } from '@/master/types/tool';

export interface FlowRecord extends Global.Entity {
  wfName: string;
  workflowVersion: string;
  wfDescription: string;
  sysDesWorkflowId: string;
}

/**
 * 工作流参数接口
 */
export interface FlowParams {
  id?: string;
  sysDesWorkflowId: string;
  powerjobWorkflowId?: string;
  workflowVersion: number;
  wfName: string;
  wfDescription: string;
  appId: string;
  timeExpressionType: '1' | '2' | '3';
  timeExpression: string;
  maxWfInstanceNum: number;
  enable: boolean;
  notifyUserIds: string[];
  globalData: string | any[];
  globalDataMap: string | any[];
  nodeDataMap: string | any[];
  pageStructure: string;
  status: 0 | 1;
  dag: { nodes: DagNode[]; edges: DagEdge[] };
  lifeCycle: {
    start: 0;
    end: 0;
  };
}

/**
 * DAG节点接口
 */
export interface DagNode {
  componentId: string;
  nodeId?: number;
  pageNodeId: string;
  nodeType: number;
  jobId?: number;
  nodeName: string;
  instanceId?: string;
  nodeParams?: string;
  status?: number;
  result?: string;
  enable: boolean;
  disableByControlNode: boolean;
  skipWhenFailed: boolean;
  startTime?: string;
  finishedTime?: string;
  subWorkflowId?: number;
  cycleArray?: string;
  workflowInstanceId?: string;
}

/**
 * DAG边接口
 */
export interface DagEdge {
  from: string;
  to: string;
  expression?: string;
}

/**
 * 工作流运行信息接口
 */
export interface RunningInfo {
  wfInstanceId: string;
  workflowId: string;
  workflowName: string;
  status: number;
  wfInitParams: null;
  result: string;
  expectedTriggerTime: string;
  actualTriggerTime: string;
  finishedTime: string;
  peworkflowDAG: {
    nodes: DagNode[];
    edges: DagEdge[];
  };
}

/**
 * 工作流运行信息参数接口
 */
export interface RunningInfoParams {
  wfInstanceId: string;
  appId: string;
}

/**
 * 工作流节点数据接口
 */
export interface WorkflowNodeData {
  sysDesComponentId: string;
  nodeType: number;
  jobId?: number;
  componentName: string;
  componentType: number;
  status?: number;
  pageStructure?: string;
  subWorkflowId?: number;
  cycleArray?: any[];
}

export interface Condition {
  id: string;
  condition: string;
}

export interface ConditionNodeData extends Tool {
  description: string;
  conditions: Condition[];
  isCollapsed?: boolean;
}
