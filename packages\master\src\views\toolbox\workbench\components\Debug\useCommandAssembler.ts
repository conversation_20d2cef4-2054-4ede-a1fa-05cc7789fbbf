import { MethodType } from '../../constants';
import type { Command, Params } from '@/master/types/tool';
import type { SoftWareCommand } from '@/master/types/software';
import type { DebugBinding } from './types';

export function useCommandAssembler() {
  function assembleCommands(
    commandList: SoftWareCommand[],
    input: Params[],
    output: Params[],
    jsonObj: DebugBinding[],
    modelPath: string
  ): Command[] {
    const openCommand = findCommandByName(commandList, '打开模型');
    const closeCommand = findCommandByName(commandList, '关闭模型');
    const saveCommand = findCommandByName(commandList, '保存模型');

    if (!openCommand || !closeCommand || !saveCommand) {
      throw new Error('必要的命令未找到');
    }

    return [
      createOpenModelCommand(openCommand.commandCode, modelPath, 0),
      ...createInputParamsCommands(input, jsonObj, 1),
      createSaveModelCommand(
        saveCommand.commandCode,
        modelPath,
        input.length + 2
      ),
      ...createOutputParamsCommands(output, input.length + 3),
      createCloseModelCommand(
        closeCommand.commandCode,
        input.length + output.length + 4
      ),
    ];
  }

  function findCommandByName(
    commandList: SoftWareCommand[],
    name: string
  ): SoftWareCommand | undefined {
    return commandList.find(command => command.commandName === name);
  }

  function createOpenModelCommand(
    commandCode: string,
    modelPath: string,
    sortNo: number
  ): Command {
    return {
      method: MethodType.CMD,
      name: commandCode,
      unit: '',
      sortNo,
      isReturnToParam: false,
      value: modelPath,
    };
  }

  function createSaveModelCommand(
    commandCode: string,
    modelPath: string,
    sortNo: number
  ): Command {
    return {
      method: MethodType.CMD,
      name: commandCode,
      unit: '',
      sortNo,
      isReturnToParam: false,
      value: modelPath,
    };
  }

  function createCloseModelCommand(
    commandCode: string,
    sortNo: number
  ): Command {
    return {
      method: MethodType.CMD,
      name: commandCode,
      unit: '',
      sortNo,
      isReturnToParam: false,
      value: '',
    };
  }

  function createInputParamsCommands(
    params: Params[],
    jsonObj: DebugBinding[],
    startSortNo: number
  ): Command[] {
    const obj = jsonObj.reduce(
      (result, current) => {
        result[current.props.label] = current.props.value;
        return result;
      },
      {} as Record<string, any>
    );
    return params
      .filter(r => r.label in obj)
      .map((param, index) => ({
        method: MethodType.SetValue,
        name: param.commandCode,
        unit: param.unit,
        sortNo: startSortNo + index,
        isReturnToParam: false,
        value: obj[param.label],
      }));
  }

  function createOutputParamsCommands(
    params: Params[],
    startSortNo: number
  ): Command[] {
    return params.map((param, index) => ({
      method: MethodType.GetValue,
      name: param.commandCode,
      unit: param.unit,
      sortNo: startSortNo + index,
      isReturnToParam: false,
      value: '',
    }));
  }

  return {
    assembleCommands,
  };
}
