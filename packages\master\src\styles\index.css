@import url('shared/styles/index.css');

@tailwind components;
@tailwind utilities;

.x6-widget-selection-inner {
  border-radius: 4px;
  border: 1px solid #239edd !important;
}

.x6-edge:hover path:nth-child(2) {
  stroke: var(--accent-100);
  stroke-width: 2px;
}

.x6-edge-selected path:nth-child(2) {
  stroke: var(--accent-100);
  stroke-width: 2px !important;
}

.x6-widget-selection-box {
  border-color: var(--accent-100);
  border-style: solid;
  border-radius: 8px;
}

.x6-widget-selection-rubberband {
  border: 1px solid var(--accent-100);
  background-color: rgba(35, 158, 221, 0.1);
}

@keyframes running-line {
  to {
    stroke-dashoffset: -1000;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
