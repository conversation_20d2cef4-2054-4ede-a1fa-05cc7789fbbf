export default {
  border: {
    label: '高度',
    value: 200,
    comp: 'a-input',
    compProps: {
      allowClear: true,
    },
  },
  textAlign: {
    label: '对齐方式',
    value: 'left',
    comp: 'a-select',
    compProps: {
      options: [
        { label: '左对齐', value: 'left' },
        { label: '右对齐', value: 'right' },
        { label: '居中对齐', value: 'center' },
      ],
    },
  },
} as Widget.StyleConfig;
