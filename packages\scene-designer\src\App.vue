<template>
  <a-config-provider :locale="zhCN">
    <style-provider hash-priority="high">
      <KeepAlive>
        <component :is="component" :schema="schemaStore.jsonSchema" />
      </KeepAlive>
    </style-provider>
  </a-config-provider>
</template>
<script setup lang="ts">
import { StyleProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { WorkBench, Preview } from '.';

import useWorkbenchStore from './stores/workbench';
import useSchemaStore from './stores/schema';

const workbenchStore = useWorkbenchStore();
const schemaStore = useSchemaStore();

const component = computed(() =>
  workbenchStore.mode === 'design' ? WorkBench : Preview
);
</script>
