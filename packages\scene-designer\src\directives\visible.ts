import { Directive } from 'vue';

const callbackMap = new Map<string, Function>();

const io = new IntersectionObserver(
  (enties, observer) => {
    for (const entry of enties) {
      const entityId = (entry.target as HTMLElement).dataset.id!;
      const callback = callbackMap.get(entityId);
      if (entry.isIntersecting) {
        callback && callback();
        observer.unobserve(entry.target);
        callbackMap.delete(entityId);
      }
    }
  },
  {
    threshold: 0,
  }
);

const visible: Directive<HTMLElement, Function> = {
  mounted(el, { value }) {
    io.observe(el);
    callbackMap.set(el.dataset.id!, value);
  },
  unmounted(el) {
    callbackMap.delete(el.dataset.id!);
  },
};

export default visible;
