<template>
  <div class="debug-panel" :class="{ 'debug-panel--collapsed': isCollapsed }">
    <div class="debug-panel__header">
      <span class="debug-panel__title">调试日志</span>
      <div class="debug-panel__actions">
        <MinusOutlined
          v-if="!isCollapsed"
          class="debug-panel__action-icon"
          aria-label="折叠面板"
          @click="toggleCollapse"
        />
        <PlusOutlined
          v-else
          class="debug-panel__action-icon"
          aria-label="展开面板"
          @click="toggleCollapse"
        />
      </div>
    </div>
    <div v-show="!isCollapsed" class="debug-panel__content">
      <a-empty v-if="debugLogs.length === 0" description="暂无调试日志" />
      <div v-else class="rounded bg-black text-white p-2 overflow-auto">
        <p
          v-for="(item, index) in debugLogs"
          :key="`${item.time}-${index}`"
          class="mb-1 last:mb-0"
        >
          <span class="text-gray-400 mr-2">{{ item.time }}</span>
          <span>{{ item.log }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import MinusOutlined from '~icons/ant-design/minus-outlined';
import PlusOutlined from '~icons/ant-design/plus-outlined';
import { useToolContext } from '../../useTool';
import { useDebugLog } from './useDebugLog';
import { useDebugProcess } from './useDebugProcess';
import type { DebugBinding } from './types';

// 定义组件事件
const emits = defineEmits<{
  'debug:complete': [value: string];
}>();

// 获取工具上下文
const { toolParams } = useToolContext();

// 控制面板折叠状态
const isCollapsed = ref(true);

// 使用调试日志钩子
const {
  debugLogs,
  addLog,
  logDebugStart,
  logSoftwareRunning,
  logDebugComplete,
} = useDebugLog();

// 使用调试进程钩子
const { startDebug: startDebugProcess } = useDebugProcess(
  toolParams,
  result => emits('debug:complete', result),
  { addLog, logDebugStart, logSoftwareRunning, logDebugComplete }
);

// 处理面板折叠
function toggleCollapse(): void {
  isCollapsed.value = !isCollapsed.value;
}

// 开始调试
function startDebug(bindings: DebugBinding[]): void {
  try {
    startDebugProcess(bindings);
  } catch (error) {
    console.error('调试过程中发生错误:', error);
    // 可以在这里添加错误处理逻辑，比如显示错误消息
  }
}

// 暴露组件方法和属性
defineExpose({
  collapsed: isCollapsed,
  startDebug,
});
</script>

<style lang="less" scoped>
.debug-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;

  &--collapsed {
    height: 48px;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    color: #1f1f1f;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  &__action-icon {
    font-size: 14px;
    color: #595959;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: #1f1f1f;
    }
  }

  &__content {
    height: 300px;
    overflow-y: auto;
    padding: 16px;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c8c8c;
    gap: 8px;

    .anticon {
      font-size: 24px;
    }
  }
}
</style>
