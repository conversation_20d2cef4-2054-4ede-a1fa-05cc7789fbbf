import dayjs from 'dayjs';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import 'dayjs/locale/zh-cn';
import './styles/index.css';
import directives from 'shared/directives';
import { createPinia } from 'pinia';
import { storage } from '@kd/utils';

dayjs.locale('zh-cn');

const app = createApp(App);
const pinia = createPinia();
app.use(router);
app.use(pinia);
app.use(directives);

const jwt = new URLSearchParams(location.search).get('jwt');

if (jwt) {
  storage.set('authorization', jwt);
}

app.mount('#app');
