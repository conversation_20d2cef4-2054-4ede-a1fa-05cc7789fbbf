import { Input, InputProps } from 'ant-design-vue';
export default ({
  label,
  style,
  unit,
  ...props
}: InputProps & {
  label: string;
  style?: Record<string, string>;
  unit?: string;
  [k: string]: any;
}) => {
  return (
    <div
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        marginRight: '10px',
        marginBottom: '10px',
      }}
    >
      {label && (
        <label style="white-space: nowrap;padding:0 8px;">{label}:</label>
      )}
      <Input {...props} style={{ minWidth: `220px` }}></Input>
    </div>
  );
};
