<template>
  <a-card>
    <template #title>
      <span class="flex items-center">
        <UilSoftware class="mr-1" />专业软件:
        <span>{{ softwareInfo?.softwareName }}</span>
      </span>
    </template>
    <template #extra>
      <a-space>
        <a-button type="link" @click="toggleTree(true)">展开全部</a-button>
        <a-button type="link" @click="toggleTree(false)">收起全部</a-button>
      </a-space>
    </template>

    <a-form :model="formState" layout="inline">
      <a-form-item
        label="模型"
        name="modelId"
        :rules="[{ required: true, message: '请选择模型' }]"
      >
        <a-select
          v-model:value="formState.modelId"
          style="width: 150px"
          placeholder="请选择模型"
          allow-clear
          :dropdown-match-select-width="false"
        >
          <a-select-option
            v-for="(item, index) in modelStore.models"
            :key="index"
            :value="item.sysDesModelId"
          >
            {{ item.modelName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="参数名">
        <a-input
          v-model:value="formState.dataCode"
          allow-clear
          placeholder="输入参数名模糊搜索"
        />
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button
            type="primary"
            :disabled="!formState.modelId"
            @click="handleSearch"
          >
            解析
          </a-button>
          <a-button
            type="primary"
            ghost
            :disabled="checkedKeys.length === 0"
            @click="addParams('in')"
          >
            抽取为输入参数
          </a-button>
          <a-button
            type="primary"
            ghost
            :disabled="checkedKeys.length === 0"
            @click="addParams('out')"
          >
            抽取为输出参数
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
    <a-config-provider
      :theme="{
        components: {
          Divider: {
            marginLG: 8,
          },
        },
      }"
    >
      <a-divider />
    </a-config-provider>
    <a-spin :spinning="loading">
      <div class="tree-container">
        <a-tree
          v-model:checked-keys="checkedKeys"
          :tree-data="treeData"
          :field-names="{
            key: 'sysDesModelDataId',
            title: 'dataName',
            children: 'children',
          }"
          :height="treeHeight"
          :checkable="true"
          :show-line="{ showLeafIcon: false }"
          :expanded-keys="expandedKeys"
          :selectable="true"
          :block-node="true"
          @expand="(keys: any[]) => (expandedKeys = keys)"
        >
          <template #switcherIcon="{ expanded }">
            <FolderOpenOutlined v-if="expanded" />
            <FolderOutlined v-else />
          </template>
          <template
            #title="{ dataCode, dataValue, dataUnit, parameterType, isLeaf }"
          >
            <div class="tree-node-content">
              <span class="tree-node-title">
                <!-- 添加节点类型图标 -->
                <span class="node-icon">
                  <span :class="[isLeaf ? 'file-icon' : 'folder-icon']"></span>
                </span>
                <strong>{{ dataCode }}</strong>
                <template v-if="isLeaf">
                  <span
                    v-if="parameterType === '1'"
                    class="tip-tag bg-blue-400"
                  >
                    入
                  </span>
                  <span
                    v-if="parameterType === '2'"
                    class="tip-tag bg-orange-400"
                  >
                    出
                  </span>
                </template>
              </span>
              <span class="tree-node-value">
                {{ dataValue || '无数据' }}
              </span>
              <span class="tree-node-unit">
                {{ dataUnit || '-' }}
              </span>
            </div>
          </template>
        </a-tree>
        <a-empty v-if="!treeData.length"></a-empty>
      </div>
    </a-spin>
  </a-card>
</template>

<script setup lang="ts">
import { queryById } from '@/master/apis/model-tool';
import useModelStore from '@/master/stores/model';
import { useToolContext } from '../../../useTool';
import { useRequest } from 'vue-hooks-plus';
import { ModelData } from '@/master/types/model';
import { message } from 'ant-design-vue';
import FolderOpenOutlined from '~icons/ant-design/folder-open-outlined';
import FolderOutlined from '~icons/ant-design/folder-outlined';
import UilSoftware from '~icons/hugeicons/software';
import type { Params } from '@/master/types/tool';

interface ModelTreeData extends ModelData {
  children: ModelTreeData[];
  key: string;
}

const modelStore = useModelStore();

const { toolParams } = useToolContext();

const route = useRoute();
const checkedKeys = ref<string[]>([]);
const treeData = ref<ModelTreeData[]>([]);
const allKeys = ref<string[]>([]);
// 存储展开的节点键值
const expandedKeys = ref<string[]>([]);
// 树高度，可根据需要使用
const treeHeight = computed(() => document.documentElement.clientHeight - 314);

// 将扁平数据结构转换为树形结构

const softwareId = route.query.professionalSoftwareId as string;
const softwareInfo = modelStore.getSoftwareById(softwareId);

const formState = reactive({
  software: softwareInfo?.softwareName,
  modelId: undefined,
  dataCode: undefined,
});

const clearSelectedRecords = () => {
  checkedKeys.value = [];
};

// 优化后的添加参数函数
const addParams = async (type: 'in' | 'out') => {
  if (!modelData.value) return;

  // 确保toolParams.value[paramsKey]是一个数组
  const paramsKey = type === 'in' ? 'inputParams' : 'outputParams';
  if (!Array.isArray(toolParams.value[paramsKey])) {
    toolParams.value[paramsKey] = [];
  }

  // 使用Set提高查找效率
  const checkedKeysSet = new Set(checkedKeys.value);
  const selectedItems = modelData.value.filter(item =>
    checkedKeysSet.has(item.sysDesModelDataId)
  );

  if (selectedItems.length === 0) return;

  // 检查参数类型冲突
  const oppositeType = type === 'in' ? '2' : '1';
  const oppositeTypeLabel = type === 'in' ? '输出' : '输入';

  // 使用some可以在找到第一个冲突项时立即停止遍历
  const hasConflict = selectedItems.some(
    el => el.parameterType === oppositeType
  );

  if (hasConflict) {
    // 只有在确认有冲突时才进行完整过滤和消息构建
    const conflictItems = selectedItems.filter(
      el => el.parameterType === oppositeType
    );
    const vals = conflictItems.map(el => el.dataCode).join(',');
    message.error(`${vals}只能作为${oppositeTypeLabel}参数`);
    return clearSelectedRecords();
  }

  // 获取当前已有的参数项的key集合，用于去重
  const existingKeys = new Set(
    toolParams.value[paramsKey].map(item => item.key)
  );

  // 预先分配数组大小以提高性能
  const paramsToAdd: Params[] = [];

  // 一次性处理所有选中项，减少循环次数
  for (const item of selectedItems) {
    // 跳过已存在的项
    if (existingKeys.has(item.sysDesModelDataId)) continue;

    const parentNode = findParentNode(modelData.value!, item);
    paramsToAdd.push({
      name: '',
      commandCode: item.commandCode,
      title: `${parentNode.reverse().join('.')}.${item.dataCode}`,
      key: item.sysDesModelDataId,
      value: item.dataValue,
      unit: item.dataUnit || '',
      defaultValue: item.dataValue,
      type,
      label: item.dataName || item.dataCode,
    });
  }

  // 只有在有新参数时才更新
  if (paramsToAdd.length > 0) {
    toolParams.value[paramsKey] = [
      ...toolParams.value[paramsKey],
      ...paramsToAdd,
    ];
  }

  clearSelectedRecords();
};

const handleSearch = () => {
  clearSelectedRecords();
  toolParams.value.sysDesModelId = formState.modelId!;
  toolParams.value.inputParams = [];
  toolParams.value.outputParams = [];
  run({
    sysDesModelId: formState.modelId!,
    dataCode: formState.dataCode || undefined,
  });
};

// 优化后的findParentNode函数，使用Map缓存节点查找
const findParentNode = (data: ModelData[], current: ModelData) => {
  const result: string[] = [];

  // 只需遍历一次数据集来构建映射
  if (!findParentNode.nodeMap) {
    findParentNode.nodeMap = new Map<string, ModelData>();
    for (const item of data) {
      findParentNode.nodeMap.set(item.sysDesModelDataId, item);
    }
  }

  // 使用映射表快速查找父节点
  let currentNode = current;
  while (currentNode.parentId) {
    const parentNode = findParentNode.nodeMap.get(currentNode.parentId);
    if (parentNode) {
      result.push(parentNode.dataCode);
      currentNode = parentNode;
    } else {
      break; // 找不到父节点，退出循环
    }
  }

  return result;
};

// 添加静态属性用于缓存
findParentNode.nodeMap = null as Map<string, ModelData> | null;

// 优化后的processTreeAndKeyData函数，减少数据遍历次数
const processTreeAndKeyData = (data: ModelData[]) => {
  const result: ModelTreeData[] = [];
  const map: Record<string, ModelTreeData> = {};
  const keys: string[] = [];

  // 单次遍历同时完成映射表创建和键值收集
  for (const item of data) {
    // 创建映射并收集键值
    map[item.sysDesModelDataId] = {
      ...item,
      key: item.sysDesModelDataId,
      children: [],
    };
    keys.push(item.sysDesModelDataId);

    // 处理父子关系
    const parentId = item.parentId?.trim();

    // 如果当前节点有父节点，且父节点已经在映射表中
    if (parentId && parentId !== '-1' && map[parentId]) {
      // 将当前节点添加到父节点的children中
      map[parentId].children.push(map[item.sysDesModelDataId]);
    }
    // 如果当前节点没有父节点，或者父节点还未处理（可能在后面的数据中）
    else if (!parentId || parentId === '-1') {
      // 如果确定是根节点，直接添加到结果数组
      result.push(map[item.sysDesModelDataId]);
    }
    // 如果父节点ID存在但尚未在map中找到，说明父节点在数据的后面部分
  }

  // 第二次遍历处理那些父节点在后面出现的情况
  // 这种情况在实际数据中可能很少，但为了保证正确性需要处理
  for (const item of data) {
    const parentId = item.parentId?.trim();
    // 如果有父节点，但节点不在结果数组中，也不是其他节点的子节点
    if (parentId && parentId !== '-1' && !map[parentId]) {
      // 作为根节点添加到结果数组
      if (!result.includes(map[item.sysDesModelDataId])) {
        result.push(map[item.sysDesModelDataId]);
      }
    }
  }

  return { processedTreeData: result, keys };
};

const {
  loading,
  data: modelData,
  run,
} = useRequest(queryById, {
  manual: true,
  formatResult: res => res.records,
  onSuccess: res => {
    const { processedTreeData, keys } = processTreeAndKeyData(res);
    treeData.value = processedTreeData;
    allKeys.value = keys;
    expandedKeys.value = keys;
  },
});

const toggleTree = (checked: boolean) => {
  if (checked) {
    // 展开全部
    expandedKeys.value = allKeys.value;
  } else {
    // 收起全部
    expandedKeys.value = [];
  }
};

onMounted(() => {
  modelStore.initModels({
    data: {
      desProfessionalSoftwareId: softwareId,
    },
    pageNum: 1,
    pageSize: 9999,
  });
});
</script>

<style lang="less" scoped>
.tree-node-content {
  display: flex;
  align-items: center;

  .tree-node-title {
    flex: 3;
    strong {
      color: var(--text-200);
    }
  }

  .tree-node-value {
    flex: 2;
    color: #666;
  }

  .tree-node-unit {
    flex: 1;
    color: #999;
    font-style: italic;
  }
}

// 自定义节点图标
.node-icon {
  display: inline-block;
  width: 20px;
  height: 100%;
  vertical-align: middle;

  .folder-icon {
    display: inline-block;
    width: 14px;
    height: 12px;
    background: linear-gradient(to bottom, var(--primary-color) 4px, #eee 0);
    border-radius: 0 2px 2px 2px;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      width: 6px;
      height: 2px;
      border-radius: 0 2px 0 0;
      background: var(--primary-color);
      top: -2px;
      left: 0;
    }
  }

  .file-icon {
    display: inline-block;
    width: 12px;
    height: 14px;
    border: 1px solid var(--primary-color);
    border-radius: 1px;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      width: 6px;
      height: 1px;
      background: var(--primary-color);
      top: 3px;
      left: 2px;
    }

    &:after {
      content: '';
      position: absolute;
      width: 6px;
      height: 1px;
      background: var(--primary-color);
      top: 6px;
      left: 2px;
    }
  }
}
.tip-tag {
  border-radius: 8px;
  padding: 0 2px;
  color: #fff;
  font-size: 12px;
  margin-left: 4px;
}
</style>
