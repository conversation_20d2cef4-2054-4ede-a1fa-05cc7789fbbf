import { showDialog } from 'dialog-async';
import { withModifiers } from 'vue';
import SelectModal from '@/scene-designer/components/SelectModal';
import Uploader from '@/scene-designer/components/Uploader';
import { Button } from 'ant-design-vue';

export default defineComponent({
  name: 'ImageSelect',
  props: {
    value: String,
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    const src = ref(props.value);

    const handleClick = () => {
      showDialog(
        <SelectModal>
          <Uploader />
        </SelectModal>
      )
        .then((value: any) => {
          src.value = value;
          emit('update:value', value);
          emit('change', value);
        })
        .catch(() => {});
    };
    return () => (
      <div onClick={withModifiers(handleClick, ['stop'])}>
        {src.value ? (
          <img src={src.value} alt="图片" />
        ) : (
          <Button type="primary">请选择</Button>
        )}
      </div>
    );
  },
});
