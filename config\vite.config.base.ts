import { resolve } from 'node:path';
import { loadEnv, UserConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { visualizer } from 'rollup-plugin-visualizer';
import AutoImport from 'unplugin-auto-import/vite';
import svgLoader from 'vite-svg-loader';
import { createHtmlPlugin } from 'vite-plugin-html';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import vueJsx from '@vitejs/plugin-vue-jsx';
import topLevelAwait from 'vite-plugin-top-level-await';
import VueSetupExtend from 'vite-plugin-vue-setup-extend';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import viteCompression from 'vite-plugin-compression';
import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import';
import { FileSystemIconLoader } from 'unplugin-icons/loaders';

export default (workPath: string, mode: string, command: 'serve' | 'build') => {
  const { VITE_APP_TITLE } = loadEnv(mode, process.cwd());
  return {
    resolve: {
      alias: {
        '@/master': resolve(workPath, '../master', './src'),
        '@/approval-process': resolve(workPath, '../approval-process', './src'),
        '@/scene-designer': resolve(workPath, '../scene-designer', './src'),
      },
      extensions: ['.vue', '.js', '.ts', '.tsx'],
    },
    plugins: [
      vue(),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        dts: resolve(workPath, './src/auto-imports.d.ts'),
        eslintrc: {
          enabled: true,
        },
        vueTemplate: true,
        injectAtEnd: true,
        defaultExportByFilename: true,
      }),
      lazyImport({
        resolvers: [
          VxeResolver({
            libraryName: 'vxe-table',
          }),
          VxeResolver({
            libraryName: 'vxe-pc-ui',
          }),
        ],
      }),
      Components({
        dirs: [resolve(workPath, 'src/components')],
        extensions: ['vue', 'tsx'],
        resolvers: [
          AntDesignVueResolver({ importStyle: false }),
          IconsResolver({
            prefix: false,
            alias: {},
          }),
        ],
        dts: resolve(workPath, './src/components.d.ts'),
      }),
      Icons({
        compiler: 'vue3',
        autoInstall: true,
        customCollections: {
          my: FileSystemIconLoader(resolve(workPath, './src/assets/svg')),
        },
      }),
      vueJsx(),
      svgLoader(),
      createHtmlPlugin({
        inject: {
          data: {
            title: VITE_APP_TITLE,
          },
        },
      }),
      // federation(getMfConfig()),
      topLevelAwait({
        // The export name of top-level await promise for each chunk module
        promiseExportName: '__tla',
        // The function to generate import names of top-level await promise in each chunk module
        promiseImportName: i => `__tla_${i}`,
      }),
      VueSetupExtend(),
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 10240,
        algorithm: 'gzip',
        ext: '.gz',
      }),
      mode === 'stats' && visualizer(),
    ],
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
        scss: {
          api: 'modern-compiler',
        },
      },
    },
    esbuild: {
      jsxFactory: 'h',
      jsxFragment: 'Fragment',
      jsxInject: "import { h } from 'vue';",
      drop: command === 'build' ? ['console', 'debugger'] : [],
    },
  } as UserConfig;
};
