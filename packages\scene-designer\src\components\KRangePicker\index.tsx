import { RangePicker, DatePickerProps } from 'ant-design-vue';

export default ({
  label,
  style,
  ...input
}: DatePickerProps & { style?: Record<string, string>; label: string }) => {
  return (
    <div
      style={{
        display: 'inline-flex',
        alignItems: 'center',
      }}
    >
      {label && (
        <label style="white-space: nowrap;padding:0 8px;">{label}:</label>
      )}
      <RangePicker
        {...(input as any)}
        style={{ minWidth: '220px' }}
      ></RangePicker>
    </div>
  );
};
