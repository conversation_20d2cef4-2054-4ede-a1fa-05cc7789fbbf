export enum ToolType {
  /** 开始节点 */
  START = 1,
  /** 软件节点 */
  SOFT = 2,
  /** 条件节点 */
  CONDITION = 3,
  /** 循环节点 */
  LOOP = 4,
  /** 群组节点 */
  GROUP = 5,
  /** 结束节点 */
  END = 9,
}

export enum ToolStatus {
  WAIT_APPROVAL = '0',
  PUBLISHED = '1',
}

export enum ParamType {
  input = 'input',
  output = 'output',
}
export enum MethodType {
  GetValue = 'GetValue',
  SetValue = 'SetValue',
  CMD = 'CMD',
}
