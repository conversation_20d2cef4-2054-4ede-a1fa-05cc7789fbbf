type Listeners = Record<string, Set<Function>>;

type EventName = keyof Listeners;

class EventEmitter<R> {
  private listeners: Listeners = {};

  on(eventName: EventName, listener: (args: any) => void) {
    if (!this.listeners[eventName]) this.listeners[eventName] = new Set();
    this.listeners[eventName].add(listener);
  }

  emit(eventName: EventName, ...args: any[]) {
    this.listeners[eventName].forEach(listener => listener.apply(null, args));
  }
}

export default new EventEmitter();
