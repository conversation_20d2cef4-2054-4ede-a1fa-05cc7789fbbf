import { defineComponent } from 'vue';
import type { PropType } from 'vue';
import { TOptionsitem } from '@/scene-designer/types/common';
import OptionsConfigModal from '../OptionsConfigModal';

export default defineComponent({
  name: 'RenderCheckbox',
  props: {
    value: Array as PropType<TOptionsitem[]>,
    options: Array as PropType<any[]>,
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    return () => (
      <OptionsConfigModal
        value={props.value}
        options={props.options}
        onChange={val => emit('change', val)}
      />
    );
  },
});
