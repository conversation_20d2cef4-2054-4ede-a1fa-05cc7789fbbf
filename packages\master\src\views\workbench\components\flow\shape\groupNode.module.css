.groupNode {
  position: relative;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(6, 7, 9, 0.15);
  border-radius: 10px;
  padding: 2px;
  width: 100%;
  height: 100%;
  box-shadow:
    rgba(0, 0, 0, 0.04) 0px 2px 6px 0px,
    rgba(0, 0, 0, 0.02) 0px 4px 12px 0px;
  color: #5361f3;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
  overflow: visible;
}

.groupNode:hover {
  border: 2px dashed var(--primary-400);
}

.nodeHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #e8e8e8;
  cursor: move;
}
.transform {
  transform: rotate(90deg);
}
.nodeHeaderIcon {
  padding: 4px;
  cursor: pointer;
  transition: transform 0.3s;
}
.nodeName {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.businessNodeName {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  bottom: -25px;
  text-align: center;
  font-weight: bold;
  color: var(--text-200);
}

.subflowContainer {
  flex: 1;
  width: 100%;
  background-color: rgba(83, 97, 243, 0.05);
  border-radius: 6px;
  margin-top: 5px;
  position: relative;
  z-index: 1;
}

/* 当子节点拖动到容器上方时的高亮样式 */
.groupNode.highlight {
  border: 2px dashed #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
}

/* 子节点在容器内的样式 */
.groupNode .x6-node {
  z-index: 10;
}

/* 添加节点选中时的高亮边框样式 */
.selected {
  border: 1px solid var(--primary-200);
  box-shadow: 0 0 8px rgba(83, 97, 243, 0.4);
}
