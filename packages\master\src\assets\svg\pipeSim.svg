<svg width="100%" height="100%"  viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <!-- 渐变背景 -->
    <defs>
        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#2867b2; stop-opacity:1" />
            <stop offset="100%" style="stop-color:#0e4498; stop-opacity:1" />
        </linearGradient>
    </defs>
    <!-- 背景矩形 -->
    <rect width="100%" height="100%" fill="currentColor" rx="15" ry="15"  />
    <!-- 文字 "PIP" -->
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="48" text-anchor="middle" dominant-baseline="middle" fill="white">
        PIP
    </text>
</svg>