<template>
  <div class="node-setting">
    <h3 class="text-lg font-medium mb-4">{{ getNodeTitle }}</h3>

    <!-- 根据节点类型显示不同的设置面板 -->
    <component :is="currentSettingComponent" :node="node" :graph="graph" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Node, Graph } from '@antv/x6';
import ApprovalNodeSetting from './ApprovalNodeSetting.vue';
import ConditionNodeSetting from './ConditionNodeSetting.vue';

// 组件属性
const props = defineProps<{
  node: Node;
  graph?: Graph;
}>();

// 获取节点数据
const nodeData = computed(() => props.node.getData() || {});

// 获取节点标题
const getNodeTitle = computed(() => {
  const shape = props.node.shape;

  if (shape === 'approval-node') {
    return nodeData.value.title || '审批节点设置';
  } else if (shape === 'condition-node') {
    return '条件分支设置';
  }

  return '节点设置';
});

// 根据节点类型获取对应的设置组件
const currentSettingComponent = computed(() => {
  const shape = props.node.shape;

  if (shape === 'approval-node') {
    return ApprovalNodeSetting;
  } else if (shape === 'condition-node') {
    return ConditionNodeSetting;
  }

  return null;
});
</script>

<style scoped>
.node-setting {
  height: 100%;
}
</style>
