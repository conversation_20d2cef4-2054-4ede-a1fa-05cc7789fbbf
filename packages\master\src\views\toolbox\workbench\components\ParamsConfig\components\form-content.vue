<template>
  <a-config-provider
    :theme="{
      components: {
        Form: {
          marginLG: 12,
        },
      },
    }"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ style: 'width: 80px' }"
    >
      <a-form-item label="参数名称" name="name" required>
        <a-input
          v-model:value="formState.name"
          placeholder="请输入参数名称"
          @change="handleChange"
        />
      </a-form-item>
      <a-form-item label="参数类别" name="type" required>
        <a-select
          v-model:value="formState.type"
          style="width: 100%"
          placeholder="请选择参数类别"
          @change="handleChange"
        >
          <a-select-option value="in"> 输入参数 </a-select-option>
          <a-select-option value="out"> 输出参数 </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="参数单位" name="dataUnit">
        <a-input v-model:value="formState.unit" placeholder="请输入参数单位" />
      </a-form-item>
      <a-form-item label="参数值" name="value">
        <a-input v-model:value="formState.value" placeholder="请输入参数值" />
      </a-form-item>
    </a-form>
  </a-config-provider>
</template>
<script setup lang="ts">
import type { Params } from '@/master/types/tool';
import type { FormInstance } from 'ant-design-vue';
import { generateUUID } from 'shared/utils';

const emits = defineEmits(['dataChange']);

const formState = ref<Partial<Params>>({
  key: generateUUID(),
});

const formRef = useTemplateRef<FormInstance>('formRef');

const handleChange = () => {
  emits('dataChange', formState.value);
};

const validate = async () => {
  const result = await formRef.value?.validateFields();
  return result;
};

const setState = (data: Params) => {
  formState.value = data;
};

defineExpose({
  validate,
  setState,
});
</script>
<style scoped lang="less"></style>
