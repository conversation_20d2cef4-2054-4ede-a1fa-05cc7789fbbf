import { Modal } from 'ant-design-vue';
import { useDialog } from 'dialog-async';

export default defineComponent({
  name: 'SelectModal',
  props: {
    title: String,
    width: {
      type: [String, Number],
      default: '50%',
    },
  },
  emits: ['submit'],
  setup(_, context) {
    const slots = useSlots();
    const dialog = useDialog();
    const handleSubmit = () => {
      dialog.submit();
      context.emit('submit');
    };
    return { dialog, handleSubmit, slots };
  },
  render() {
    return (
      <Modal
        title={this.title}
        centered
        width={this.width}
        open={this.dialog.visible}
        onCancel={this.dialog.cancel}
        onOk={this.handleSubmit}
      >
        <div style={{ padding: '8px' }}></div>
        {this.slots?.default && this.slots.default()}
      </Modal>
    );
  },
});
