<template>
  <a-menu v-model:selected-keys="selectedKeys" theme="light" :items> </a-menu>
</template>
<script lang="ts" setup>
import type { ItemType } from 'ant-design-vue';
import AntDesignAppstoreOutlined from '~icons/ant-design/appstore-outlined';
const selectedKeys = ref([]);
const items: ItemType[] = [
  {
    key: 'component',
    label: '组件列表',
    icon: () => h(AntDesignAppstoreOutlined),
  },
];
</script>
