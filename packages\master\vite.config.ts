import { defineConfig, UserConfig } from 'vite';
import devConfig from '../../config/vite.config.dev';
import baseConfig from '../../config/vite.config.base';
import buildConfig from '../../config/vite.config.build';

export default defineConfig(({ command, mode }) => {
  const result: UserConfig = {
    ...baseConfig(__dirname, mode, command),
    ...devConfig(4000, mode),
    ...buildConfig(command),
  };
  return result;
});
