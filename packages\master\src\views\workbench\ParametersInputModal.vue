<template>
  <a-modal
    :title="modalTitle"
    :open="dialog.visible"
    :mask-closable="false"
    width="80%"
    @cancel="handleSubmit"
  >
    <Preview :schema="pageSchema" @value-change="handleValueChange" />
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit">执行</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
/**
 * @file ParametersInputModal.vue
 * @description 参数输入弹窗组件
 */
import { Tool } from '@/master/types/tool';
import { useDialog } from 'dialog-async';
import { Preview } from 'scene-designer';

/**
 * 组件属性定义
 */
const { nodeData, pageSchema } = defineProps<{
  /** 页面结构数据 */
  pageSchema: any[];
  /** 节点名称 */
  nodeData: Tool;
}>();

/**
 * 弹窗标题
 */
const modalTitle = computed(() => `${nodeData.componentName}自定义页面`);

/**
 * 输入参数
 */
const inputParams = ref<any[]>([]);

/**
 * 对话框实例
 */
const dialog = useDialog();

/**
 * 处理值变更事件
 * @param {any[]} events - 值变更事件数据
 */
const handleValueChange = (events: any[]): void => {
  inputParams.value = events;
};

/**
 * 取消操作
 */
const handleCancel = (): void => {
  dialog.cancel();
};

/**
 * 提交参数
 */
const handleSubmit = (): void => {
  // 提前解析参数映射并转换为Map提高查找效率
  // const nodeParamsMap = new Map(
  //   (JSON.parse(nodeData.paramsMap) as any[]).map(item => [
  //     item.paramCode,
  //     item.command,
  //   ])
  // );
  // 转换输入参数为命令参数对象
  const commandParams = inputParams.value.reduce<Record<string, string>>(
    (result, { id, props }) => {
      result[id] = props.value;
      return result;
    },
    {}
  );
  dialog.submit(commandParams);
};
</script>
