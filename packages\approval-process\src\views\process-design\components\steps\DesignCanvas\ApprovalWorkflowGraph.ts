/**
 * @file ApprovalWorkflowGraph.ts
 * @description 审批流程图形核心类，用于管理审批流程图形的创建、编辑和交互
 */

import { Graph, Node, Edge } from '@antv/x6';
import { Snapline } from '@antv/x6-plugin-snapline';
import { History } from '@antv/x6-plugin-history';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Transform } from '@antv/x6-plugin-transform';
import { generateUUID } from 'shared/utils';
import { registerApprovalNodes } from './plugins/registerNodes';
import { registerApprovalEdges } from './plugins/registerEdges';
import { registerApprovalEdgeTools } from './plugins/registerEdgeTools';
import { createApp, h } from 'vue';
import EdgeAddMenu from './components/EdgeAddMenu.vue';
/**
 * 审批流程图形类
 */
export class ApprovalWorkflowGraph extends Graph {
  /** 当前选中节点 */
  public currentNode: Node | undefined = undefined;

  /**
   * 构造函数
   * @param container - 容器元素
   */
  constructor(container: HTMLElement) {
    if (!(container instanceof HTMLElement)) {
      throw new Error('未找到有效的容器元素');
    }

    // 创建图形实例，调用父类构造函数
    super({
      container,
      autoResize: true,
      grid: false,
      // 鼠标滚轮缩放配置
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        minScale: 0.5,
        maxScale: 3,
      },
      // 启用画布平移
      panning: {
        enabled: true,
        modifiers: 'alt', // 按住 Alt 键时可以平移画布
      },
      // 禁用节点拖动
      interacting: {
        nodeMovable: false, // 禁止节点移动
        edgeMovable: false, // 禁止边移动
        vertexMovable: false, // 禁止路径点移动
        arrowheadMovable: false, // 禁止箭头移动
        edgeLabelMovable: false, // 禁止边标签移动
      },
      // 连线配置
      connecting: {
        allowBlank: false, // 不允许连接到空白区域
        allowMulti: false, // 不允许多重连接
        allowLoop: false, // 不允许自环
        allowNode: false, // 不允许连接到节点
        allowEdge: false, // 不允许连接到边
        allowPort: true, // 允许连接到连接桩
        router: 'orth', // 曲线路由算法
        connector: 'normal', // 连接器类型
        anchor: 'center', // 锚点位置
        // 创建连线
        createEdge() {
          return this.createEdge({
            shape: 'approval-edge',
            zIndex: 0,
          });
        },
      },
    });

    // 初始化插件、事件和组件
    this.initialize();
  }

  /**
   * 初始化图形
   */
  initialize(): void {
    // 初始化插件
    this.initPlugins();

    // 初始化事件
    this.initEvents();

    // 注册节点和边
    this.registerShapes();
  }

  /**
   * 初始化插件
   */
  initPlugins(): void {
    // 添加对齐线插件
    this.use(
      new Snapline({
        enabled: true,
      })
    );
    this.use(
      // 添加撤销重做插件
      new History({
        enabled: true,
      })
    );
    this.use(
      // 添加键盘插件
      new Keyboard({
        enabled: true,
      })
    );
    this.use(
      // 添加变换插件，支持画布平移
      new Transform({
        resizing: false, // 禁用调整大小
        rotating: false, // 禁用旋转
      })
    );
  }

  /**
   * 初始化事件
   */
  initEvents(): void {
    // 节点移动事件
    this.on('node:change:position', () => {
      // 更新连接线
      this.updateConnections();
    });

    // 连接创建事件
    this.on('edge:connected', ({ edge }) => {
      // 为新创建的边添加工具
      if (edge.shape === 'approval-edge') {
        this.addEdgeTools(edge);
      }
    });

    // 为所有边添加工具
    this.on('edge:added', ({ edge }) => {
      if (edge.shape === 'approval-edge') {
        this.addEdgeTools(edge);
      }
    });
  }

  /**
   * 为边添加工具
   * @param edge - 边实例
   */
  addEdgeTools(edge: Edge): void {
    edge.addTools([
      {
        name: 'edge-add-button',
      },
    ]);
  }

  /**
   * 注册节点和边
   */
  registerShapes(): void {
    registerApprovalNodes();
    registerApprovalEdges();
    registerApprovalEdgeTools();
  }

  /**
   * 边上添加按钮菜单实例
   */
  private edgeAddMenuInstance: any = null;

  /**
   * 当前选中的边
   */
  private activeEdge: Edge | null = null;

  /**
   * 切换边上的添加菜单
   * @param e - 鼠标事件
   * @param edge - 边实例
   */
  toggleEdgeAddMenu(e: MouseEvent, edge: Edge): void {
    // 阻止事件冒泡，防止立即触发document的点击事件
    e.stopPropagation();

    // 如果已经有菜单实例，先移除它
    this.removeEdgeAddMenu();

    // 创建菜单容器
    const menuContainer = document.createElement('div');
    document.body.appendChild(menuContainer);

    // 计算菜单位置，将菜单显示在按钮下方
    const position = {
      x: e.clientX + 60,
      y: e.clientY + 10, // 向下偏移20像素，避免覆盖按钮
    };

    // 保存当前边
    this.activeEdge = edge;

    // 创建一个文档点击处理函数的引用，以便在移除菜单时可以正确移除事件监听器
    const handleDocumentClick = (event: MouseEvent) => {
      // 检查点击的目标是否是菜单容器或其子元素
      if (!menuContainer.contains(event.target as unknown as HTMLElement)) {
        this.removeEdgeAddMenu();
      }
    };

    // 创建菜单组件
    const menuApp = createApp({
      render: () => {
        return h(EdgeAddMenu, {
          visible: true,
          position,
          edge,
          onAddNode: (type: string) => {
            if (this.activeEdge) {
              this.handleAddNodeOnEdge(this.activeEdge, type);
            }
            this.removeEdgeAddMenu();
          },
          onAddCondition: () => {
            if (this.activeEdge) {
              this.handleAddConditionOnEdge(this.activeEdge);
            }
            this.removeEdgeAddMenu();
          },
          onClose: () => {
            this.removeEdgeAddMenu();
          },
        });
      },
    });

    // 挂载菜单
    menuApp.mount(menuContainer);

    // 保存菜单实例和文档点击处理函数
    this.edgeAddMenuInstance = {
      app: menuApp,
      container: menuContainer,
      documentClickHandler: handleDocumentClick,
    };

    // 使用更长的延迟，确保当前点击事件处理完毕后再添加监听器
    setTimeout(() => {
      document.addEventListener('mousedown', handleDocumentClick);
    }, 100);
  }

  /**
   * 移除边上的添加菜单
   */
  removeEdgeAddMenu(): void {
    if (this.edgeAddMenuInstance) {
      // 移除文档点击事件监听器
      if (this.edgeAddMenuInstance.documentClickHandler) {
        document.removeEventListener(
          'mousedown',
          this.edgeAddMenuInstance.documentClickHandler
        );
      }

      // 卸载Vue应用
      this.edgeAddMenuInstance.app.unmount();

      // 从DOM中移除容器
      if (document.body.contains(this.edgeAddMenuInstance.container)) {
        document.body.removeChild(this.edgeAddMenuInstance.container);
      }

      // 清除引用
      this.edgeAddMenuInstance = null;
      this.activeEdge = null;
    }
  }

  /**
   * 在边上添加节点
   * @param edge - 边实例
   * @param nodeType - 节点类型

   */
  handleAddNodeOnEdge(edge: Edge, nodeType: string): void {
    // 获取源节点和目标节点
    const sourceNode = edge.getSourceNode();
    const targetNode = edge.getTargetNode();
    if (!sourceNode || !targetNode) return;

    // 计算新节点位置
    const sourcePosition = sourceNode.getPosition();
    const targetPosition = targetNode.getPosition();
    const x = (sourcePosition.x + targetPosition.x) / 2;
    const y = (sourcePosition.y + targetPosition.y) / 2;

    // 创建新节点
    const newNode = this.addNode({
      id: generateUUID(),
      shape: 'approval-node',
      x: x - 90, // 居中
      y: y - 40, // 居中
      width: 180,
      height: 80,
      data: {
        type: nodeType,
        title:
          nodeType === 'approval'
            ? '审批人'
            : nodeType === 'cc'
              ? '抄送人'
              : '办理人',
        description:
          nodeType === 'approval'
            ? '请设置审批人'
            : nodeType === 'cc'
              ? '请设置抄送人'
              : '请设置办理人',
        approvers: [],
      },
    });
    // 删除原来的边
    this.removeEdge(edge);

    // 添加新的边 (源节点 -> 新节点)
    this.addEdge({
      source: { cell: sourceNode, port: 'bottom' },
      target: { cell: newNode, port: 'top' },
      shape: 'approval-edge',
    });

    // 添加新的边 (新节点 -> 目标节点)
    this.addEdge({
      source: { cell: newNode, port: 'bottom' },
      target: { cell: targetNode, port: 'top' },
      shape: 'approval-edge',
    });

    // 执行自定义工作流布局
    this.doLayout();
  }

  /**
   * 在边上添加条件分支
   * @param edge - 边实例
   */
  handleAddConditionOnEdge(edge: Edge): void {
    // 获取源节点和目标节点
    const sourceNode = edge.getSourceNode();
    const targetNode = edge.getTargetNode();

    if (!sourceNode || !targetNode) return;

    // 计算新节点位置
    const sourcePosition = sourceNode.getPosition();
    const targetPosition = targetNode.getPosition();
    const x = (sourcePosition.x + targetPosition.x) / 2;
    const y = (sourcePosition.y + targetPosition.y) / 2;

    // 创建条件分支节点
    const conditionNode = this.addConditionNode(x - 120, y - 60, sourceNode.id);

    // 为每个条件添加连接桩
    conditionNode.addPort({
      id: 'port-0',
      group: 'left',
    });

    conditionNode.addPort({
      id: 'port-1',
      group: 'right',
    });

    // 删除原来的边
    this.removeEdge(edge);

    // 添加新的边 (源节点 -> 条件节点)
    this.addEdge({
      source: { cell: sourceNode, port: 'bottom' },
      target: { cell: conditionNode, port: 'top' },
      shape: 'approval-edge',
    });

    // 创建条件分支1
    const condition1 = this.addNode({
      id: generateUUID(),
      shape: 'approval-node',
      x: x - 180,
      y: y + 100,
      width: 180,
      height: 80,
      data: {
        type: 'condition',
        title: '条件 优先级1',
        description: '请设置审批条件',
      },
    });

    // 创建条件分支2
    const condition2 = this.addNode({
      id: generateUUID(),
      shape: 'approval-node',
      x: x + 60,
      y: y + 100,
      width: 180,
      height: 80,
      data: {
        type: 'condition',
        title: '条件 优先级2',
        description: '其他条件进入此流程',
      },
    });

    // 添加新的边 (条件节点 -> 条件分支1)
    this.addEdge({
      source: { cell: conditionNode, port: 'port-0' },
      target: { cell: condition1, port: 'top' },
      shape: 'normal-edge',
    });

    // 添加新的边 (条件节点 -> 条件分支2)
    this.addEdge({
      source: { cell: conditionNode, port: 'port-1' },
      target: { cell: condition2, port: 'top' },
      shape: 'normal-edge',
    });

    // 添加新的边 (条件分支1 -> 目标节点)
    this.addEdge({
      source: { cell: condition1, port: 'bottom' },
      target: { cell: targetNode, port: 'top' },
      shape: 'approval-edge',
    });

    // 添加新的边 (条件分支2 -> 目标节点)
    this.addEdge({
      source: { cell: condition2, port: 'bottom' },
      target: { cell: targetNode, port: 'top' },
      shape: 'approval-edge',
    });

    // 执行自定义工作流布局
    this.doLayout();
  }

  /**
   * 更新连接线
   */
  updateConnections(): void {
    const edges = this.getEdges();
    edges.forEach(edge => {
      edge.setZIndex(0);
    });
  }

  /**
   * 添加条件分支节点
   * @param x - x坐标
   * @param y - y坐标
   * @param sourceNodeId - 源节点ID
   */
  addConditionNode(x: number, y: number, sourceNodeId: string): Node {
    const id = generateUUID();
    return this.addNode({
      id,
      shape: 'condition-node',
      x,
      y,
      width: 106,
      height: 32,
      data: {
        title: '条件分支',
        sourceNodeId,
      },
    });
  }

  /**
   * 处理添加分支
   * @param conditionNode - 条件节点
   */
  handleAddBranch(conditionNode: Node): void {
    if (!conditionNode) return;

    // 获取条件节点数据
    const nodeData = conditionNode.getData();
    if (!nodeData) return;

    // 获取条件节点位置和大小
    const position = conditionNode.getPosition();
    const size = conditionNode.getSize();

    // 获取条件节点的所有出边
    const outgoingEdges = this.getOutgoingEdges(conditionNode);
    if (!outgoingEdges || outgoingEdges.length === 0) return;

    // 获取目标节点（所有条件分支连接的节点）
    // 首先获取第一个条件分支节点
    const firstConditionNode = outgoingEdges[0].getTargetNode();
    if (!firstConditionNode) return;

    // 然后获取从条件分支节点出发的边
    const conditionOutgoingEdges = this.getOutgoingEdges(firstConditionNode);
    if (!conditionOutgoingEdges || conditionOutgoingEdges.length === 0) return;

    // 获取最终的目标节点
    const targetNode = conditionOutgoingEdges[0].getTargetNode();
    if (!targetNode) return;

    // 获取现有条件分支节点，用于计算新节点的位置
    const existingConditionNodes = outgoingEdges.map(edge =>
      edge.getTargetNode()
    );
    const lastConditionNode =
      existingConditionNodes[existingConditionNodes.length - 1];
    const lastPosition = lastConditionNode
      ? lastConditionNode.getPosition()
      : null;

    // 创建新的条件分支节点
    const newConditionIndex = outgoingEdges.length + 1;
    const newCondition = this.addNode({
      id: generateUUID(),
      shape: 'approval-node', // 使用 approval-node 形状，但设置 type 为 condition
      // 如果有上一个条件节点，则在其右侧创建新节点，否则使用默认位置
      x: lastPosition ? lastPosition.x + 240 : position.x + size.width / 2,
      y: lastPosition ? lastPosition.y : position.y + size.height + 100,
      width: 180,
      height: 80,
      data: {
        type: 'condition',
        title: `条件 优先级${newConditionIndex}`,
        description: '请设置审批条件',
      },
    });

    // 为条件节点添加新的连接桩
    const portId = `port-${outgoingEdges.length}`;
    conditionNode.addPort({
      id: portId,
      group: 'right',
    });

    // 添加新的边 (条件节点 -> 新条件分支)
    this.addEdge({
      source: { cell: conditionNode, port: portId },
      target: { cell: newCondition, port: 'top' },
      shape: 'normal-edge',
    });

    // 添加新的边 (新条件分支 -> 目标节点)
    this.addEdge({
      source: { cell: newCondition, port: 'bottom' },
      target: { cell: targetNode, port: 'top' },
      shape: 'approval-edge',
    });

    // 执行自定义工作流布局
    this.doLayout();
  }

  /**
   * 自动布局
   * 使用改进的分层布局算法对节点进行自动布局，特别优化条件分支的处理
   */
  doLayout(): void {
    // 第七步：居中显示内容
    this.centerContent();
  }

  /**
   * 更新添加按钮节点的位置
   * 在布局完成后调整添加按钮的位置
   */
  private updateAddButtonPositions(): void {
    const addButtons = this.getNodes().filter(
      node => node.shape === 'add-button'
    );

    addButtons.forEach(button => {
      const data = button.getData();
      if (data && data.sourceNodeId) {
        const sourceNode = this.getCellById(data.sourceNodeId) as Node;
        const targetNode =
          this.getOutgoingEdges(sourceNode)?.[0]?.getTargetNode();

        if (sourceNode && targetNode) {
          const sourcePos = sourceNode.getPosition();
          const targetPos = targetNode.getPosition();
          const buttonSize = button.getSize();

          // 计算边的中点位置
          const x =
            (sourcePos.x + targetPos.x) / 2 +
            sourceNode.getSize().width / 2 -
            buttonSize.width / 2;
          const y = (sourcePos.y + targetPos.y) / 2;

          button.position(x, y);
        }
      }
    });
  }

  /**
   * 放大画布
   * @param factor - 放大因子，默认为 0.1
   */
  zoomIn(factor: number = 0.1): void {
    const zoom = this.zoom();
    this.zoomTo(zoom + factor);
  }

  /**
   * 缩小画布
   * @param factor - 缩小因子，默认为 0.1
   */
  zoomOut(factor: number = 0.1): void {
    const zoom = this.zoom();
    this.zoomTo(Math.max(0.1, zoom - factor));
  }

  /**
   * 重置画布视图
   * 将画布缩放重置为 1，并居中显示内容
   */
  resetView(): void {
    this.zoomTo(1);
    this.centerContent();
  }
}
