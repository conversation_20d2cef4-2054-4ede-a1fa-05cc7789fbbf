export default {
  type: {
    label: '类型',
    value: 'primary',
    comp: 'a-select',
    compProps: {
      options: [
        { label: '主要按钮', value: 'primary' },
        { label: '次要按钮', value: 'default' },
        { label: '警告按钮', value: 'dashed' },
        { label: '幽灵按钮', value: 'ghost' },
        { label: '文本', value: 'text' },
        { label: '链接', value: 'link' },
      ],
    },
  },
  text: {
    label: '文本',
    value: '确定',
    comp: 'a-input',
  },
  icon: {
    label: '图标',
    value: '',
    comp: 'a-input',
  },
} as Widget.PropsConfig;
