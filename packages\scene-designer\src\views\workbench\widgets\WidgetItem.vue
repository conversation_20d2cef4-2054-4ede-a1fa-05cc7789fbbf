<template>
  <div
    class="widget-item"
    :style="{
      opacity:
        workbenchStore.isDragging && workbenchStore.draggingWidget === item.key
          ? 0.5
          : 1,
    }"
  >
    <div ref="el" class="widget-item__icon">
      <component :is="item.icon" class="widget-item__icon-svg" />
    </div>
    <p class="widget-item__label">{{ item.name }}</p>
  </div>
</template>
<script lang="ts" setup>
import { draggable } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import type { CleanupFn } from '@atlaskit/pragmatic-drag-and-drop/dist/types/internal-types';
import useWorkbenchStore from '@/scene-designer/stores/workbench';

const { item } = defineProps<{ item: Widget.ResourceConfig }>();

const el = useTemplateRef<HTMLElement>('el');
const workbenchStore = useWorkbenchStore();

const cleanup = ref<CleanupFn>();

onMounted(() => {
  if (!el.value) return;
  cleanup.value = draggable({
    element: el.value,
    getInitialData: () => ({ key: item.key }),
    onDragStart: () => {
      workbenchStore.setIsDragging(true);
      workbenchStore.setDraggingWidget(item.key);
    },
    onDrop: () => {
      workbenchStore.setIsDragging(false);
    },
  });
});

onBeforeUnmount(() => {
  cleanup.value && cleanup.value();
});
</script>
<style lang="less">
.widget-item {
  color: var(--primary-100);
  padding: 4px;
  overflow: hidden;
  &__icon {
    display: flex;
    cursor: grab;
    height: 52px;
    background-color: var(--bg-200);
    border-radius: 4px;
    border: 1px solid var(--bg-100);
    transition: 0.15s ease-in-out;
    transition-property: border-color;
    &:hover {
      border-color: var(--bg-300);
    }
    &-svg {
      width: 80%;
      height: 80%;
      margin: auto;
    }
  }
  &__label {
    white-space: nowrap;
    font-size: 10px;
    font-weight: 500;
    line-height: 12px;
    margin: 4px -2px 0;
    text-align: center;
    color: var(--text-200);
  }
}
</style>
