import PipeSim02 from '@/scene-designer/assets/images/pipesim-new.jpg';

export default ({
  style,
  src,
  alt,
}: {
  style: Record<string, any>;
  src: string;
  alt: string;
}) => {
  const styled = {
    width: style.width
      ? `${style.width}${style.width.toString().includes('%') ? '' : 'px'}`
      : undefined,
    height: style.height ? `${style.height}px` : undefined,
  };

  return (
    <div class="inline-block">
      <img style={styled} src={src || PipeSim02} alt={alt || '图片'} />
    </div>
  );
};
