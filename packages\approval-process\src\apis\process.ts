import { request } from '@kd/utils';
import type { Process, ProcessQueryParams } from '../types/process';

/**
 * 获取流程列表
 * @param params 查询参数
 * @returns 流程列表
 */
export const getProcessList = (params: ProcessQueryParams) =>
  request<Global.ListResponse<Process>>({
    url: '/workflow/process/list',
    method: 'POST',
    data: params,
  });

/**
 * 获取流程详情
 * @param id 流程ID
 * @returns 流程详情
 */
export const getProcessDetail = (id: string) =>
  request<Process>({
    url: '/workflow/process/detail',
    method: 'GET',
    params: { id },
  });

/**
 * 创建流程
 * @param data 流程数据
 * @returns 创建结果
 */
export const createProcess = (data: Partial<Process>) =>
  request<string>({
    url: '/workflow/process/create',
    method: 'POST',
    data,
  });

/**
 * 更新流程
 * @param data 流程数据
 * @returns 更新结果
 */
export const updateProcess = (data: Partial<Process>) =>
  request<boolean>({
    url: '/workflow/process/update',
    method: 'POST',
    data,
  });

/**
 * 删除流程
 * @param ids 流程ID数组
 * @returns 删除结果
 */
export const deleteProcess = (ids: string[]) =>
  request<boolean>({
    url: '/workflow/process/delete',
    method: 'POST',
    data: { ids },
  });

/**
 * 更新流程状态
 * @param data 状态更新数据
 * @returns 更新结果
 */
export const updateProcessStatus = (data: { id: string; status: string }) =>
  request<boolean>({
    url: '/workflow/process/updateStatus',
    method: 'POST',
    data,
  });
