<template>
  <a-form-item :label="item.label" :name="itemKey">
    <component
      :is="item.comp"
      v-model:value="modelValue"
      v-bind="item.compProps || {}"
      @change="handleChange"
    />
  </a-form-item>
</template>

<script setup lang="ts">
/**
 * Configuration item interface
 */
interface ConfigItem {
  /** Display label for the form item */
  label: string;
  /** Component to render */
  comp: string | object;
  /** Current value */
  value: any;
  isInjectProps?: boolean;
  /** Additional component props */
  compProps?: Record<string, any>;
}

interface Props {
  /** Configuration item */
  item: ConfigItem;
  /** Item key in the configuration object */
  itemKey: string;
  /** Change handler callback */
  onChange: (params: {
    key: string;
    value: any;
    option?: Global.Option;
    isInjectProps?: boolean;
  }) => void;
}

const { itemKey, item, onChange } = defineProps<Props>();

/**
 * Computed model value that tracks the item.value changes
 */
const modelValue = ref();

watchEffect(() => {
  modelValue.value = item.value;
});

/**
 * Handle change events from components that don't support v-model
 */
const handleChange = (value: any, option?: any): void => {
  onChange({
    key: itemKey,
    value,
    isInjectProps: item.isInjectProps,
    option,
  });
};
</script>
