<template>
  <div class="h-full p-4 bg-[#fff] rounded-lg">
    <div class="bg-[#fff] rounded-lg">
      <div class="flex items-center justify-between p-2">
        <div class="flex items-center gap-5">
          <div>
            工作流ID：
            <a-input
              v-model:value="search.workflowId"
              placeholder="请输入工作流ID"
              style="width: 220px"
              allow-clear
            />
          </div>
          <div>
            工作流实例ID：
            <a-input
              v-model:value="search.wfInstanceId"
              placeholder="请输入工作流实例ID"
              style="width: 220px"
            />
          </div>
          <div>
            状态：
            <a-select
              v-model:value="search.status"
              :options="statusList"
              style="width: 100px"
            />
          </div>
          <a-button type="primary" @click="getTableData">搜索</a-button>
          <a-button type="primary" @click="handleReset">重置</a-button>
        </div>

        <a-button type="primary" @click="getTableData">刷新</a-button>
      </div>

      <a-table
        :data-source="tableData"
        :loading="tableLoading"
        :columns="columns"
        :scroll="{ y: tableHeight }"
        :pagination="{
          current: page.current,
          pageSize: page.size,
          total: page.total,
          onChange: (current, size) => {
            page.current = current;
            page.size = size;
            getTableData();
          },
        }"
        size="small"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { message, TableColumnsType } from 'ant-design-vue';
import {
  retryWfInstance,
  stopWorkflowInstance,
  workflowInstanceList,
} from '@/master/apis/flow';
import { statusList } from './helper';

const router = useRouter();
const route = useRoute();
const search = ref({
  workflowId: '',
  wfInstanceId: '',
  status: '',
});

const tableData = ref([]);
const columns = computed<TableColumnsType>(() => [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1,
  },
  { title: '工作流ID', dataIndex: 'workflowId' },
  { title: '工作流名称', dataIndex: 'workflowName' },
  { title: '工作流实例ID', dataIndex: 'wfInstanceId' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => statusList.find(s => s.key === text)?.label,
  },
  { title: '触发时间', dataIndex: 'actualTriggerTime' },
  { title: '结束时间', dataIndex: 'finishedTime' },
  {
    title: '操作',
    key: 'action',
    width: 240,
    customRender: ({ record }: { record: any }) => (
      <div>
        <a-button type="link" onClick={() => toDetail(record)}>
          详情
        </a-button>
        <a-button type="link" danger onClick={() => handleStop(record)}>
          停止
        </a-button>
        <a-button
          type="link"
          style={{ color: '#e6a33d' }}
          onClick={() => handleRetry(record)}
        >
          重试
        </a-button>
      </div>
    ),
  },
]);
const tableHeight = ref(window.innerHeight - 250);
const tableLoading = ref<boolean>(false);

const page = ref({
  current: 1,
  size: 20,
  total: 0,
});

const getTableData = async () => {
  tableLoading.value = true;
  const params = {
    appId: '16',
    index: page.value.current - 1,
    pageSize: page.value.size,
    wfInstanceId: search.value.wfInstanceId,
    workflowId: search.value.workflowId,
    status: search.value.status,
  };
  const res = await workflowInstanceList(params);
  if (res[0].data.length > 0) {
    const it = res[0];
    tableData.value = it.data;
    page.value = {
      current: it.index + 1,
      size: it.pageSize,
      total: it.totalItems,
    };
  } else {
    tableData.value = [];
    page.value.total = 0;
  }
  tableLoading.value = false;
};

const handleReset = () => {
  page.value = {
    current: 1,
    size: 20,
    total: 0,
  };
  search.value = {
    workflowId: '',
    wfInstanceId: '',
    status: '',
  };
  getTableData();
};

const toDetail = async (row: any) => {
  router.push({
    path: '/system-setting/workflow-instance/detail',
    query: {
      id: row.wfInstanceId,
    },
  });
};

const handleStop = async (row: any) => {
  try {
    await stopWorkflowInstance(row.wfInstanceId);
  } catch (error: any) {
    message.error(error);
  }
};

const handleRetry = async (row: any) => {
  const params = {
    appId: '16',
    wfInstanceId: row.wfInstanceId,
  };

  try {
    await retryWfInstance(params);
  } catch (error) {
    console.log('error :>>', error);
    // message.error(error);
  }
};

onMounted(() => {
  getTableData();

  window.addEventListener('resize', () => {
    tableHeight.value = window.innerHeight - 250;
  });

  const id = route.query.sysDesWorkflowId as string;
  if (id) {
    search.value.workflowId = route.query.sysDesWorkflowId as string;
    getTableData();
  }
});
</script>

<style lang="less" scoped></style>
