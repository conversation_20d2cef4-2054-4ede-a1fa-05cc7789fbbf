<template>
  <a-card :title="title" class="col-span-2 h-[300px]">
    <a-timeline>
      <a-timeline-item
        v-for="(activity, index) in activities"
        :key="index"
        :color="getActivityColor(activity.type)"
      >
        <template #dot>
          <component :is="getActivityIcon(activity.type)" />
        </template>
        <div class="flex justify-between">
          <div>
            <span class="font-medium">{{ activity.user }}</span>
            <span class="ml-2">{{ activity.action }}</span>
            <span class="ml-2 text-gray-500">{{ activity.process }}</span>
          </div>
          <span class="text-gray-400">{{ activity.time }}</span>
        </div>
      </a-timeline-item>
    </a-timeline>
  </a-card>
</template>

<script setup lang="ts">
import { getActivityColor, getActivityIcon } from '../utils';
import type { ApprovalActivity } from '@/approval-process/types/approval';

defineProps<{
  title: string;
  activities: ApprovalActivity[];
}>();
</script>
