import type { Tool, ToolCategory as BaseToolCategory } from '@/master/types/tool';

/**
 * 扩展的工具分类接口，用于工具箱组件
 * 这里的 children 是 Tool[] 而不是 ToolCategory[]
 */
export interface ToolCategoryWithTools extends Omit<BaseToolCategory, 'children'> {
  children?: Tool[];
}

/**
 * 视图模式类型
 */
export type ViewMode = 'table' | 'card';

/**
 * 工具箱常量
 */
export const TOOLBOX_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 999,
  GRID_MIN_WIDTH: 100,
  CARD_HEIGHT: 300,
} as const;
