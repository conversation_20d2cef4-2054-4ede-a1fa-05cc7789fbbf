/**
 * @file useApprovalWorkflow.ts
 * @description 审批流程图形上下文钩子，用于共享审批流程图形状态
 */

import { createContextHook } from '@kd/hooks';
import { Node } from '@antv/x6';
import { ApprovalWorkflowGraph } from './ApprovalWorkflowGraph';

/**
 * 创建审批流程图形上下文钩子
 */
export const { useApprovalWorkflow, createApprovalWorkflow } =
  createContextHook('ApprovalWorkflow', () => {
    /**
     * 工作流图形实例
     */
    const workflow = shallowRef<ApprovalWorkflowGraph>();

    /**
     * 当前选中节点
     */
    const currentNode = shallowRef<Node>();

    /**
     * 初始化图形
     * @param container - 容器元素
     */
    const initWorkflow = (container: HTMLElement): ApprovalWorkflowGraph => {
      // 创建工作流图形实例
      workflow.value = new ApprovalWorkflowGraph(container);

      // 监听节点选择事件
      workflow.value.on('node:click', ({ node }) => {
        // 获取节点数据
        const nodeData = node.getData() || {};
        // 如果是条件节点或结束节点，不显示设置面板
        if (node.shape === 'condition-node' || nodeData.type === 'end') return;
        currentNode.value = node;
      });

      // 监听画布点击事件（取消选择）
      workflow.value.on('blank:click', () => {
        currentNode.value = undefined;
      });

      return workflow.value;
    };

    /**
     * 添加审批节点
     * @param x - x坐标
     * @param y - y坐标
     * @param type - 节点类型
     * @param title - 节点标题
     * @param description - 节点描述
     */
    const addApprovalNode = (
      x: number,
      y: number,
      type: string,
      title: string,
      description: string
    ): Node | undefined => {
      if (!workflow.value) return;

      return workflow.value.addNode({
        shape: 'approval-node',
        x,
        y,
        width: 180,
        height: 80,
        data: {
          type,
          title,
          description,
          approvers: [],
        },
      });
    };

    /**
     * 添加条件分支节点
     * @param x - x坐标
     * @param y - y坐标
     * @param sourceNodeId - 源节点ID
     */
    const addConditionNode = (
      x: number,
      y: number,
      sourceNodeId: string
    ): Node | undefined => {
      if (!workflow.value) return;

      return workflow.value.addConditionNode(x, y, sourceNodeId);
    };

    /**
     * 删除节点
     * @param nodeId - 节点ID
     */
    const removeNode = (nodeId: string): void => {
      if (!workflow.value) return;

      const node = workflow.value.getCellById(nodeId);
      if (node) {
        workflow.value.removeCell(node);
      }
    };

    /**
     * 更新节点数据
     * @param nodeId - 节点ID
     * @param data - 节点数据
     */
    const updateNodeData = (nodeId: string, data: any): void => {
      if (!workflow.value) return;

      const node = workflow.value.getCellById(nodeId) as Node;
      if (node) {
        const currentData = node.getData() || {};
        node.setData({
          ...currentData,
          ...data,
        });
      }
    };

    return {
      workflow,
      currentNode,
      initWorkflow,
      addApprovalNode,
      addConditionNode,
      removeNode,
      updateNodeData,
    };
  });
