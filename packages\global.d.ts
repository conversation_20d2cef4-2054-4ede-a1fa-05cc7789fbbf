declare interface Window {
  __POWERED_BY_WUJIE__: boolean;
}

declare namespace Global {
  interface Response<T = unknown> {
    code: string;
    data: T;
    msg: string;
  }
  interface ListResponse<T = unknown> {
    records: T[];
    current: number;
    pages: number;
    size: number;
    total: number;
  }
  type Key = string | number;
  type Option = {
    key?: string;
    label: string;
    value: any;
    [k: string]: any;
    children?: Option[];
  };
  type GroupOption = {
    label: string;
    options?: Option[];
    value?: any;
  };

  interface Pagination {
    pageSize: number;
    pageNum: number;
  }

  interface Entity {
    bsflag: 'N' | 'Y';
    updateTime: string;
    updateUser: string;
    createUser: string;
    createTime: string;
    tenantCode?: string;
  }

  interface User {
    userId: string;
    userName: string;
    loginId: string;
    organizationName: string;
    orgId: string;
    xnOrgId: string;
    xnOrgLevel: string;
    xnUnitType: string;
    jobCodes: string[];
  }
}
