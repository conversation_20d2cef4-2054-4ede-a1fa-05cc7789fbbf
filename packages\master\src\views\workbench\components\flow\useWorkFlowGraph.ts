/**
 * @file useWorkFlowGraph.ts
 * @description 工作流图形上下文钩子，用于共享工作流图形状态
 */

import { WorkflowGraph } from './core/WorkflowGraph';
import type { FlowParams } from '@/master/types/flow';
import { createContextHook } from '@kd/hooks';

/**
 * 创建工作流图形上下文钩子
 */
export const { useWorkflowGraph, createWorkflowGraph } = createContextHook(
  'WorkflowGraph',
  () => {
    /**
     * 工作流图形实例
     */
    const workflow = shallowRef<WorkflowGraph>();

    /**
     * 初始化图形
     * @param container - 容器元素
     */
    const initGrid = (container: HTMLElement): void => {
      workflow.value = new WorkflowGraph(container);
    };

    /**
     * 工作流ID
     */
    const workflowId = ref<string>();

    /**
     * 工作流参数
     */
    const workflowParams = ref<FlowParams>({
      id: undefined,
      sysDesWorkflowId: '',
      powerjobWorkflowId: '',
      workflowVersion: 0,
      wfName: '工作流1',
      wfDescription: '',
      appId: '',
      timeExpressionType: '1',
      timeExpression: '',
      maxWfInstanceNum: 0,
      enable: true,
      notifyUserIds: [],
      globalData: [],
      globalDataMap: [],
      nodeDataMap: [],
      pageStructure: '',
      status: 1,
      dag: { nodes: [], edges: [] },
      lifeCycle: {
        start: 0,
        end: 0,
      },
    });

    return { initGrid, workflow, workflowParams, workflowId };
  }
);
