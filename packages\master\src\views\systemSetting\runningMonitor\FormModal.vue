<template>
  <a-modal
    v-model:open="dialog.visible"
    :title="isNew ? '新建' : '编辑'"
    width="40%"
    :destroy-on-close="true"
    @cancel="dialog.cancel"
  >
    <div>operational status modal</div>
    <template #footer>
      <div>
        <a-button @click="dialog.cancel">取消</a-button>
        <a-button type="primary" @click="handldSubmit">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { useDialog } from 'dialog-async';
import { debounce } from 'radash';

const { isNew, modelId, command, getList } = defineProps<{
  isNew: boolean;
  modelId?: string;
  command?: any;
  getList?: () => void;
}>();

const dialog = useDialog();

const formRef = ref();

const info = ref({});

const handldSubmit = debounce({ delay: 200 }, () => {
  console.log('submit :>>');
});

// onMounted(() => {
//   if (command) info.value = command;
// });
</script>
<style scoped lang="less"></style>
