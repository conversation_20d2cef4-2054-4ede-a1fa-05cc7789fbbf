import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import { eventEmitter } from '@kd/utils';
import { createRouterGuards } from './routerGuards';
import Page404 from 'shared/pages/404.vue';
import Layout from 'shared/layouts/index.vue';
import CarbonIbmProcessMining from '~icons/carbon/ibm-process-mining';
import MageDashboardPlus from '~icons/mage/dashboard-plus';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    name: 'home',
    redirect: '/dashboard',
    meta: { isRoot: true },
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/approval-process/views/dashboard/index.vue'),
        meta: {
          title: '工作台',
          icon: MageDashboardPlus,
        },
      },
      {
        path: 'process-manage',
        name: 'process-manage',
        component: () =>
          import('@/approval-process/views/process-manage/index.vue'),
        meta: {
          title: '流程管理',
          icon: CarbonIbmProcessMining,
        },
      },
      {
        path: 'process-design',
        name: 'process-design',
        component: () =>
          import('@/approval-process/views/process-design/index.vue'),
        meta: {
          title: '流程设计',
          showInMenu: false,
        },
      },
      {
        path: 'icon-select-test',
        name: 'icon-select-test',
        component: () =>
          import('@/approval-process/views/test/icon-select-test.vue'),
        meta: {
          title: 'IconSelect组件测试',
          showInMenu: false,
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    name: '404',
    component: Page404,
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

eventEmitter.on('API_UN_AUTH', async () => {
  await router.push('/login');
});

createRouterGuards(router);

export default router;
