<template>
  <a-modal
    v-model:open="dialog.visible"
    :title="modalTitle"
    width="50%"
    :confirm-loading="loading"
    @cancel="handleCancel"
    @ok="handleSubmit"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="FORM_LAYOUT.labelCol"
      :wrapper-col="FORM_LAYOUT.wrapperCol"
    >
      <a-form-item label="模型名称" name="modelName" required>
        <a-input
          v-model:value="formState.modelName"
          :disabled="isModelNameDisabled"
          placeholder="请输入模型名称"
          :maxlength="50"
          show-count
        />
      </a-form-item>

      <a-form-item label="专业软件" name="desProfessionalSoftwareId" required>
        <a-select
          v-model:value="formState.desProfessionalSoftwareId"
          :disabled="!!props.detail"
          :options="softwareOptions"
          @change="handleSoftwareChange"
          placeholder="请选择专业软件"
          show-search
          :filter-option="filterSoftwareOption"
        />
      </a-form-item>

      <a-form-item label="模型文件" name="modelPath" required>
        <a-upload
          :file-list="modelFileList"
          name="file"
          :max-count="1"
          :disabled="props.isUpdate"
          :multiple="false"
          :before-upload="handleModelFileUpload"
          @remove="handleModelFileRemove"
        >
          <a-button :disabled="props.isUpdate">
            <upload-outlined />
            点击上传模型文件
          </a-button>
        </a-upload>
      </a-form-item>

      <a-form-item label="模型图片" name="modelImage">
        <a-upload
          :file-list="modelImageList"
          name="file"
          :accept="ACCEPTED_IMAGE_TYPES"
          :disabled="props.isUpdate"
          :max-count="1"
          :multiple="false"
          :before-upload="handleModelImageUpload"
          @remove="handleModelImageRemove"
        >
          <a-button :disabled="props.isUpdate">
            <upload-outlined />
            点击上传模型图片
          </a-button>
        </a-upload>
      </a-form-item>

      <a-form-item label="模型描述" name="remark">
        <a-textarea
          v-model:value="formState.remark"
          :rows="4"
          placeholder="请输入模型描述"
          :maxlength="500"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { message, type FormInstance, type UploadFile } from 'ant-design-vue';
import { useDialog } from 'dialog-async';
import UploadOutlined from '~icons/ant-design/upload-outlined';

import { addModel, updateModel } from '@/master/apis/model-tool';
import { uploadModel, uploadModelImage } from '@/master/apis/model-management';
import { ModelRecord } from '@/master/types/model';
import { SoftWare } from '@/master/types/software';
import useModelStore from '@/master/stores/model';

// 常量定义
const FORM_LAYOUT = {
  labelCol: { span: 4 },
  wrapperCol: { span: 18 },
} as const;

const ACCEPTED_IMAGE_TYPES = 'image/*';

const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB

// Props 定义
interface Props {
  detail?: ModelRecord;
  isUpdate?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  detail: undefined,
  isUpdate: false,
});

// 响应式数据
const dialog = useDialog();
const modelStore = useModelStore();
const formRef = ref<FormInstance>();
const loading = ref(false);
const modelFileList = ref<UploadFile[]>([]);
const modelImageList = ref<UploadFile[]>([]);

// 表单状态
const formState = ref<Partial<ModelRecord>>({
  ...props.detail,
  parentId: props.isUpdate ? undefined : props.detail?.sysDesModelId,
});

// 计算属性
const modalTitle = computed(() => {
  if (props.isUpdate) return '编辑模型';
  return props.detail ? '添加模型版本' : '添加模型';
});

const isModelNameDisabled = computed(() => {
  return props.isUpdate ? false : !!props.detail;
});

const softwareOptions = computed(() => {
  return modelStore.software.map((item: SoftWare) => ({
    label: item.softwareName,
    value: item.desProfessionalSoftwareId,
  }));
});

// 工具函数
const validateFileSize = (file: File, maxSize: number): boolean => {
  if (file.size > maxSize) {
    message.error(`文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`);
    return false;
  }
  return true;
};

// 事件处理函数
const handleCancel = () => {
  dialog.cancel();
};

const handleSoftwareChange = (_: any, option: any) => {
  formState.value.softwareCode = option.label;
};

const filterSoftwareOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

const handleModelImageRemove = () => {
  modelImageList.value = [];
};

const handleModelFileRemove = () => {
  modelFileList.value = [];
};
const handleModelFileUpload = (file: File) => {
  if (!validateFileSize(file, MAX_FILE_SIZE)) {
    return false;
  }

  modelFileList.value = [file as any];
  formState.value.modelPath = file.name;

  // 如果模型名称为空，自动填充文件名（去掉扩展名）
  if (!formState.value.modelName) {
    const nameWithoutExt = file.name.substring(0, file.name.lastIndexOf('.'));
    formState.value.modelName = nameWithoutExt;
  }

  return false; // 阻止自动上传
};

const handleModelImageUpload = (file: File) => {
  if (!validateFileSize(file, MAX_IMAGE_SIZE)) {
    return false;
  }

  modelImageList.value = [file as any];
  formState.value.modelImage = file.name;

  return false; // 阻止自动上传
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    if (props.isUpdate) {
      // 更新模型
      await updateModel(formState.value);
      message.success('更新成功!');
    } else {
      // 添加新模型
      const { sysDesModelId } = await addModel(formState.value);
      formState.value.sysDesModelId = sysDesModelId;

      // 上传模型文件
      if (modelFileList.value.length > 0) {
        await uploadModel({
          file: modelFileList.value[0] as any,
          sysDesModelId: formState.value.sysDesModelId!,
          desProfessionalSoftwareId: formState.value.desProfessionalSoftwareId!,
        });
      }

      // 上传模型图片（可选）
      if (modelImageList.value.length > 0) {
        await uploadModelImage(
          modelImageList.value[0] as any,
          formState.value.sysDesModelId!
        );
      }

      message.success('添加成功!');
    }

    dialog.submit();
  } catch (error: any) {
    console.error('提交失败:', error);
    message.error(error?.message || '操作失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(async () => {
  if (modelStore.software.length === 0) {
    await modelStore.initSoftware();
  }
});
</script>
