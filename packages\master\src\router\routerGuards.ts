import type { Router } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { storage } from '@kd/utils';
import useUserStore from 'shared/store/user';
// 路由白名单
const routerAllowList = ['login', 'forget', 'register'];

export function createRouterGuards(router: Router) {
  router.beforeEach(async (to, _from) => {
    const userStore = useUserStore();
    const jwt = storage.get('authorization');
    if (!NProgress.isStarted()) {
      NProgress.start();
    }
    if (!routerAllowList.includes(to.name as string)) {
      if (jwt) {
        if (!userStore.userInfo.userId) {
          await userStore.setUserData(jwt);
        }
        return true;
      } else {
        location.href = `${import.meta.env.VITE_APP_LOGIN_DOMAIN}/front/uc/v3/login/xn?appId=${import.meta.env.VITE_APP_CLIENT_ID}&redirectUrl=${to.path}`;
        return false;
      }
    }
    return true;
  });

  router.afterEach(() => {
    NProgress.done();
  });

  // 错误
  router.onError(error => {
    console.log(error, '路由错误');
  });
}
