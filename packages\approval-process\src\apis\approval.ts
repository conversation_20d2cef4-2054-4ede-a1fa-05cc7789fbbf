import { request } from '@kd/utils';
import type {
  ApprovalStatistics,
  ApprovalProcess,
  InitiatedApprovalProcess,
  ApprovalActivity,
  ApprovalTrend,
  ApprovalDetail,
  ApprovalTemplate,
} from '../types/approval';

/**
 * 获取审批统计数据
 * @returns 审批统计数据
 */
export const getApprovalStatistics = () =>
  request<ApprovalStatistics>({
    url: '/workflow/approval/statistics',
    method: 'GET',
  });

/**
 * 获取待我审批列表
 * @param params 分页参数
 * @returns 待审批列表
 */
export const getPendingApprovals = (params: {
  pageNum: number;
  pageSize: number;
}) =>
  request<{
    records: ApprovalProcess[];
    current: number;
    pages: number;
    size: number;
    total: number;
  }>({
    url: '/workflow/approval/pending',
    method: 'POST',
    data: params,
  });

/**
 * 获取我发起的审批列表
 * @param params 分页参数
 * @returns 我发起的审批列表
 */
export const getInitiatedApprovals = (params: {
  pageNum: number;
  pageSize: number;
}) =>
  request<{
    records: InitiatedApprovalProcess[];
    current: number;
    pages: number;
    size: number;
    total: number;
  }>({
    url: '/workflow/approval/initiated',
    method: 'POST',
    data: params,
  });

/**
 * 获取最近审批活动
 * @param limit 限制数量
 * @returns 最近审批活动
 */
export const getRecentActivities = (limit: number = 5) =>
  request<ApprovalActivity[]>({
    url: '/workflow/approval/activities',
    method: 'GET',
    params: { limit },
  });

/**
 * 获取审批趋势数据
 * @param days 天数
 * @returns 审批趋势数据
 */
export const getApprovalTrend = (days: number = 7) =>
  request<ApprovalTrend>({
    url: '/workflow/approval/trend',
    method: 'GET',
    params: { days },
  });

/**
 * 审批流程
 * @param data 审批数据
 * @returns 审批结果
 */
export const approveProcess = (data: {
  id: string;
  action: 'approve' | 'reject';
  comment?: string;
}) =>
  request<boolean>({
    url: '/workflow/approval/approve',
    method: 'POST',
    data,
  });

/**
 * 获取审批详情
 * @param id 审批ID
 * @returns 审批详情
 */
export const getApprovalDetail = (id: string) =>
  request<ApprovalDetail>({
    url: '/workflow/approval/detail',
    method: 'GET',
    params: { id },
  });

/**
 * 获取审批模板列表
 * @returns 审批模板列表
 */
export const getApprovalTemplates = () =>
  request<ApprovalTemplate[]>({
    url: '/workflow/approval/templates',
    method: 'GET',
  });
