<template>
  <layout :show-sider="false" :title>
    <template #right>
      <div class="flex items-center gap-3">
        <div
          class="flex items-center gap-1 cursor-pointer"
          @click="handleGoHome"
        >
          <IcoMoonFreeHome />工作台
        </div>
        <a-dropdown>
          <span class="ant-dropdown-link cursor-pointer" @click.prevent>
            <SolarUserCircleBold />{{ userInfo.userName }}
          </span>
        </a-dropdown>
      </div>
    </template>
  </layout>
</template>
<script lang="ts" setup>
import useUserStore from 'shared/store/user';
import Layout from 'shared/layouts/index.vue';
import IcoMoonFreeHome from '~icons/icomoon-free/home';
import SolarUserCircleBold from '~icons/solar/user-circle-bold';

const { userInfo } = useUserStore();
const router = useRouter();
const title = import.meta.env.VITE_APP_TITLE;

const handleGoHome = () => {
  router.push('/dashboard');
};
</script>
