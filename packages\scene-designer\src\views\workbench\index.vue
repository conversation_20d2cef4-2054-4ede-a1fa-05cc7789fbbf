<template>
  <DialogProvider>
    <a-layout class="h-full">
      <a-layout-header
        v-if="showHeader"
        has-sider
        style="
          background-color: white;
          height: 40px;
          padding: 0 12px;
          border-bottom: 1px solid #ececec;
        "
      >
        <div class="workbench-header">
          <a-space>
            <a-button type="text" @click="handleSave">
              <template #icon>
                <ant-design-save-outlined />
              </template>
              保存
            </a-button>
            <a-button type="text" @click="preview">
              <template #icon>
                <ant-design-eye-outlined />
              </template>
              预览
            </a-button>
          </a-space>
        </div>
      </a-layout-header>
      <a-layout-content>
        <a-layout has-sider class="h-full">
          <a-layout-sider theme="light" width="240">
            <widgets />
          </a-layout-sider>
          <a-layout-content>
            <dashboard />
          </a-layout-content>
          <a-layout-sider
            theme="light"
            :width="300"
            :collapsed-width="0"
            :zero-width-trigger-style="{ display: 'none' }"
          >
            <config-panel>
              <template #default="slotProps">
                <slot name="inject-config" v-bind="slotProps" />
              </template>
            </config-panel>
          </a-layout-sider>
        </a-layout>
      </a-layout-content>
    </a-layout>
  </DialogProvider>
</template>
<script lang="ts" setup>
import useSchemaStore from '@/scene-designer/stores/schema';
import useWorkbenchStore from '@/scene-designer/stores/workbench';
import useConfigStore from '@/scene-designer/stores/config';
import Widgets from './widgets/index.vue';
import Dashboard from './dashboard/index.vue';
import ConfigPanel from './configPanel/index.vue';
import components from '@/scene-designer/components/index';
import { DialogProvider } from 'dialog-async';

const {
  schema,
  injectComponentProps,
  showHeader = true,
  injectSource,
} = defineProps<{
  schema?: Widget.RenderConfig[];
  showHeader?: boolean;
  injectComponentProps?: Widget.PropsConfig;
  injectSource?: any[];
}>();

const emits = defineEmits<{
  save: [value: Widget.RenderConfig[]];
  injectComponentPropsChange: [
    value: {
      value: string;
      renderSchema: Widget.RenderConfig;
      configSchema: Widget.PropsConfig;
      option?: Global.Option;
    },
  ];
}>();

const schemaStore = useSchemaStore();
const workbenchStore = useWorkbenchStore();
const configStore = useConfigStore();

const handleSave = async () => {
  const jsonSchema = schemaStore.jsonSchema;
  emits('save', jsonSchema);
};

const preview = () => {
  workbenchStore.setMode('preview');
};

const handleInjectPropsChange = (params: {
  value: string;
  renderSchema: Widget.RenderConfig;
  configSchema: Widget.PropsConfig;
  option?: Global.Option;
}) => {
  emits('injectComponentPropsChange', params);
};

const initComponent = () => {
  const currentInstance = getCurrentInstance();
  const appInstance = currentInstance?.appContext.app;
  if (appInstance) {
    appInstance.use(components);
  }
};

const initStore = () => {
  schemaStore.initSchema(schema || []);
  configStore.initPropsConfig(injectComponentProps);
  configStore.initStyleConfig();
  workbenchStore.setInjectSource(injectSource || []);
};

onMounted(() => {
  initComponent();
  initStore();
});

provide('injectPropsChange', handleInjectPropsChange);
</script>
<style lang="less">
.workbench-header {
  display: flex;
  justify-content: end;
  height: 100%;
}
.h-full {
  height: 100%;
}
</style>
