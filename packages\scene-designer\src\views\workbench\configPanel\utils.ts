/**
 * Utility functions for config panel
 */

/**
 * Check if an object is empty
 * @param obj Object to check
 * @returns True if object is empty
 */
export const isEmpty = (obj: Object): boolean =>
  typeof obj === 'object' && obj !== null && Object.keys(obj).length === 0;

/**
 * Extract value from event or direct value
 * @param e Event or direct value
 * @returns Extracted value
 */
export const extractValue = (e: any): any => {
  return e && e.target ? e.target.value : e;
};
