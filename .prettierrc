{"printWidth": 80, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": false, "trailingComma": "es5", "bracketSpacing": true, "BracketLine": true, "arrowParens": "avoid", "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "tslintIntegration": false, "endOfLine": "auto", "overrides": [{"files": "*.html", "options": {"parser": "html"}}]}