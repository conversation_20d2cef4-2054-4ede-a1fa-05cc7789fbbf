/**
 * @file WorkflowGraph.ts
 * @description 工作流图形核心类，用于管理工作流图形的创建、编辑和交互
 */

import { Graph, Node } from '@antv/x6';
import { Snapline } from '@antv/x6-plugin-snapline';
import { History } from '@antv/x6-plugin-history';
import { Selection } from '@antv/x6-plugin-selection';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Transform } from '@antv/x6-plugin-transform';
import { generateUUID } from 'shared/utils';
import eventEmitter from 'shared/utils/eventEmitter';
import type { DagNode } from '@/master/types/flow';
import {
  NODE_STATUS,
  NODE_TYPE,
  GROUP_NODE_WIDTH,
  GROUP_NODE_HEIGHT,
  GRAPH_SHAPE,
} from '../../../constants';
import { registerNode } from '../plugins/registerNode';
import { registerEdge, registerEdgeTool } from '../plugins/registerEdge';
import { initEvents } from './events';

/**
 * 工作流图形类
 */
export class WorkflowGraph extends Graph {
  /** 当前选中节点 */
  public currentNode: Node | undefined = undefined;

  /** 存储是否按下 Ctrl 键 */
  public ctrlPressed: boolean = false;

  /** 子节点与父节点边界的内边距 */
  public embedPadding: number = 20;

  /**
   * 构造函数
   * @param container - 容器元素
   */
  constructor(container: HTMLElement) {
    if (!(container instanceof HTMLElement)) {
      throw new Error('未找到有效的容器元素');
    }

    // 创建图形实例，调用父类构造函数
    super({
      container,
      autoResize: true,
      grid: true,
      // 鼠标滚轮缩放配置
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3,
      },
      // 启用画布平移
      panning: {
        enabled: true,
        modifiers: 'alt', // 按住 Alt 键时可以平移画布
      },
      // 嵌入式节点配置
      embedding: {
        enabled: true,
        findParent({ node }) {
          // 如果节点本身是分组节点或循环节点，不允许嵌入到其他节点中
          if (node.shape === GRAPH_SHAPE.GROUP) {
            return [];
          }
          const bbox = node.getBBox();
          return this.getNodes().filter(parent => {
            // 检查父节点是否为分组节点或循环节点
            if (parent.shape === GRAPH_SHAPE.GROUP) {
              const targetBBox = parent.getBBox();
              // 检查节点是否在父节点内部
              return targetBBox.containsRect(bbox);
            }
            return false;
          });
        },
      },

      // 连线配置
      connecting: {
        allowBlank: false, // 不允许连接到空白区域
        allowMulti: false, // 允许多重连接
        allowLoop: false, // 允许自环
        allowNode: false, // 允许连接到节点
        allowEdge: false, // 不允许连接到边
        allowPort: true, // 允许连接到连接桩
        router: 'manhattan', // 曲线路由算法
        connector: 'soomth', // 连接器类型
        anchor: 'center', // 锚点位置
        snap: {
          radius: 10, // 吸附半径
        },
        // 创建连线
        createEdge() {
          return this.createEdge({
            shape: 'dashed-edge',
            attrs: {
              wrap: {
                connection: true,
                strokeWidth: 10,
                strokeLinejoin: 'round',
              },
              line: {
                connection: true,
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  size: 6,
                },
              },
            },
          });
        },
        // 验证连接是否有效
        validateConnection({ sourcePort, sourceCell, targetCell, targetPort }) {
          // 不允许连接到同一个连接桩
          if (sourcePort === targetPort) return false;
          // 不允许连接到同一个节点
          return sourceCell !== targetCell;
        },
      },
      // 高亮显示配置
      highlighting: {
        embedding: {
          name: 'stroke',
          args: {
            padding: -1,
            attrs: {
              stroke: 'transparent',
            },
          },
        },
        // 连接桩可以被连接时的高亮样式
        magnetAvailable: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#fff',
              stroke: '#A4DEB1',
              strokeWidth: 4,
            },
          },
        },
        // 连接桩吸附连线时的高亮样式
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#fff',
              stroke: '#31d0c6',
              strokeWidth: 4,
            },
          },
        },
      },
    });

    // 初始化插件、事件和组件
    this.initialize();
  }

  /**
   * 初始化图形
   */
  initialize(): void {
    // 初始化插件
    this.initPlugins();

    // 初始化事件
    initEvents(this);

    // 注册节点和边
    this.registerShapes();
  }

  /**
   * 初始化插件
   */
  initPlugins(): void {
    // 添加对齐线插件
    this.use(
      new Snapline({
        enabled: true,
      })
    );
    this.use(
      // 添加撤销重做插件
      new History({
        enabled: true,
      })
    );
    this.use(
      // 添加键盘插件
      new Keyboard({
        enabled: true,
      })
    );
    this.use(
      // 添加选择插件
      new Selection({
        enabled: true,
        multiple: true, // 启用多选
        rubberband: true, // 启用框选
        rubberNode: true, // 允许框选节点
        rubberEdge: false, // 不允许框选边
        movable: true, // 选中的节点可以一起移动
        showNodeSelectionBox: true, // 显示节点选择框
        pointerEvents: 'none', // 防止选择框阻止节点事件
        className: 'node-selection', // 添加选择框样式类名
        modifiers: 'shift', // 使用 shift 键进行多选
      })
    );
    this.use(
      // 添加变换插件，支持画布平移
      new Transform({
        resizing: false, // 禁用调整大小
        rotating: false, // 禁用旋转
      })
    );
  }

  /**
   * 注册节点和边
   */
  registerShapes(): void {
    registerNode();
    registerEdge();
    registerEdgeTool();
  }

  /**
   * 自动布局
   */
  doLayout(): void {
    //  居中显示
    this.centerContent();
  }

  /**
   * 确保所有子节点都在父节点内部
   * @param groupNode - 分组节点
   */
  private ensureChildrenInsideGroup(groupNode: Node): void {
    const children = groupNode.getChildren() as Node[];
    if (!children || children.length === 0) return;

    const groupBBox = groupNode.getBBox();
    const padding = this.embedPadding;

    // 检查每个子节点
    children.forEach(child => {
      if (!(child instanceof Node)) return;

      const childBBox = child.getBBox();
      let needsRepositioning = false;
      let newX = childBBox.x;
      let newY = childBBox.y;

      // 检查左边界
      if (childBBox.x < groupBBox.x + padding) {
        newX = groupBBox.x + padding;
        needsRepositioning = true;
      }

      // 检查右边界
      if (
        childBBox.x + childBBox.width >
        groupBBox.x + groupBBox.width - padding
      ) {
        newX = groupBBox.x + groupBBox.width - childBBox.width - padding;
        needsRepositioning = true;
      }

      // 检查上边界
      if (childBBox.y < groupBBox.y + padding) {
        newY = groupBBox.y + padding;
        needsRepositioning = true;
      }

      // 检查下边界
      if (
        childBBox.y + childBBox.height >
        groupBBox.y + groupBBox.height - padding
      ) {
        newY = groupBBox.y + groupBBox.height - childBBox.height - padding;
        needsRepositioning = true;
      }

      // 如果需要重新定位，则移动子节点
      if (needsRepositioning) {
        child.position(newX, newY);
      }
    });
  }

  /**
   * 将内容居中显示
   * @param data - 可选的图形数据，如果提供则先加载数据
   * @returns 当前实例
   */
  centerContent(data?: any): this {
    if (data) {
      this.fromJSON(data);
    }
    return super.centerContent();
  }

  /**
   * 从节点添加边，将所有节点依次连接起来
   */
  addEdgesFromNodes(): void {
    // 获取所有节点
    const nodes = this.getNodes();
    if (nodes.length === 0) return;

    // 取出第一个节点作为起始节点
    let beforeNode = nodes.shift()!;

    // 依次连接每个节点
    nodes.forEach((node: Node) => {
      // 获取前一个节点的右侧连接桩
      const beforeNodePorts = beforeNode.getPortsByGroup('right');
      if (beforeNodePorts.length === 0) return;

      const beforeNodePortId = beforeNodePorts[0].id;

      // 获取当前节点的左侧连接桩
      const currentNodePorts = node.getPortsByGroup('left');
      if (currentNodePorts.length === 0) return;

      // 添加连接边
      this.addEdge({
        shape: 'dashed-edge',
        source: {
          cell: beforeNode,
          port: beforeNodePortId,
        },
        target: {
          cell: node,
          port: currentNodePorts[0].id,
        },
      });

      // 更新前一个节点
      beforeNode = node;
    });
  }

  /**
   * 获取参数列表并触发事件
   */
  getNodesParams(): void {
    // 获取所有软件节点并提取参数
    const result = this.getNodes()
      .filter((node: Node) => node.getData()?.componentType === NODE_TYPE.SOFT)
      .map((node: Node) => {
        const data = node.getData();
        // 合并输入和输出参数
        const params = JSON.parse(data.inputParams || '[]').concat(
          JSON.parse(data.outputParams || '[]')
        );

        // 构建参数树结构
        return {
          key: node.id,
          title: data.componentName,
          value: data.componentIcon,
          children: params.map((param: any) => ({
            key: param.name,
            title: param.label,
            value: param.value,
            type: param.type,
            unit: param.unit,
          })),
        };
      });

    // 触发节点变化事件
    eventEmitter.emit('node:change', result);
  }

  /**
   * 添加分组节点或循环节点
   * @param x - 节点的 x 坐标
   * @param y - 节点的 y 坐标
   * @param data - 节点数据
   * @returns 创建的节点
   */
  addGroupNode(x: number, y: number, data: any): Node {
    // 生成唯一ID
    const id = generateUUID();
    // 添加节点
    return this.addNode({
      id,
      shape: GRAPH_SHAPE.GROUP,
      x,
      y,
      width: GROUP_NODE_WIDTH,
      height: GROUP_NODE_HEIGHT,
      data: {
        ...data,
        loopCount: data.componentType === NODE_TYPE.LOOP ? 1 : undefined, // 循环节点默认循环次数为1
      },
    });
  }

  /**
   * 调整分组节点或循环节点大小
   * @param groupNode - 可选的特定节点，如果不提供则调整所有可调整大小的节点
   */
  adjustGroupNodeSize(groupNode?: Node): void {
    // 获取所有可调整大小的节点
    const adjustableNodes = groupNode
      ? [groupNode]
      : this.getNodes().filter(node => {
          return node.shape === GRAPH_SHAPE.GROUP;
        });

    // 遍历所有节点
    adjustableNodes.forEach(group => {
      // 获取节点的所有子节点
      const children = group.getChildren();

      // 如果没有子节点，保持默认大小
      if (!children || children.length === 0) {
        return;
      }

      // 获取父节点的原始大小，如果没有则记录当前大小
      let originSize = group.prop('originSize');
      if (originSize == null) {
        originSize = group.getSize();
        group.prop('originSize', originSize);
      }

      // 获取父节点的原始位置，如果没有则记录当前位置
      let originPosition = group.prop('originPosition');
      if (originPosition == null) {
        originPosition = group.getPosition();
        group.prop('originPosition', originPosition);
      }

      // 初始化父节点的边界坐标
      let x = originPosition.x;
      let y = originPosition.y;
      let cornerX = originPosition.x + originSize.width;
      let cornerY = originPosition.y + originSize.height;
      let hasChange = false;

      // 遍历所有子节点，计算新的边界
      children.forEach(child => {
        // 获取子节点的边界框，并添加内边距
        const bbox = child.getBBox().inflate(this.embedPadding);
        const corner = bbox.getCorner();

        // 更新父节点的边界
        if (bbox.x < x) {
          x = bbox.x;
          hasChange = true;
        }

        if (bbox.y < y) {
          y = bbox.y;
          hasChange = true;
        }

        if (corner.x > cornerX) {
          cornerX = corner.x;
          hasChange = true;
        }

        if (corner.y > cornerY) {
          cornerY = corner.y;
          hasChange = true;
        }
      });

      // 如果边界有变化，更新父节点的位置和大小
      if (hasChange) {
        group.prop(
          {
            position: { x, y },
            size: { width: cornerX - x, height: cornerY - y },
          },
          { skipParentHandler: true }
        );

        // 确保子节点都在父节点内部
        this.ensureChildrenInsideGroup(group);
      }
    });
  }

  /**
   * 更新边的样式
   * @param edge - 边实例
   * @param status - 节点状态
   */
  private updateEdgeStyle(edge: any, status: number | undefined): void {
    switch (status) {
      case NODE_STATUS.RUNNING:
        // 运行中状态的边样式
        edge.attr('line/strokeDasharray', 5);
        edge.attr('line/stroke', 'oklch(0.768 0.233 130.85)');
        edge.attr('line/style/animation', 'running-line 30s infinite linear');
        break;
      case NODE_STATUS.SUCCESS:
        // 成功状态的边样式
        edge.attr('line/stroke', '#2470ff');
        edge.attr('line/strokeDasharray', '');
        edge.attr('line/style/animation', '');
        break;
      case NODE_STATUS.FAILED:
        // 失败状态的边样式
        edge.attr('line/stroke', 'oklch(0.637 0.237 25.331)');
        edge.attr('line/strokeDasharray', '');
        edge.attr('line/style/animation', '');
        break;
      default:
        // 默认样式
        edge.attr('line/stroke', '#A2B1C3');
        edge.attr('line/strokeDasharray', '');
        edge.attr('line/style/animation', '');
    }
  }

  /**
   * 更新节点状态
   * @param nodes - DAG节点数组
   */
  updateNodeStatus(nodes: DagNode[]): void {
    if (!nodes || nodes.length === 0) return;

    // 创建节点ID到状态节点的映射，提高查找效率
    const statusNodeMap = new Map<string, DagNode>();
    nodes.forEach(node => {
      if (node.pageNodeId) {
        statusNodeMap.set(node.pageNodeId, node);
      }
    });

    // 获取图形中的所有节点
    const graphNodes = this.getNodes();

    // 遍历并更新每个节点的状态
    graphNodes.forEach((node: Node) => {
      const nodeId = node.id;
      const nodeWithStatus = statusNodeMap.get(nodeId);

      if (!nodeWithStatus) return;

      const nodeData = node.getData();
      const { status, result } = nodeWithStatus;

      try {
        // 解析输出参数
        const parsedOutParams = JSON.parse(
          nodeData.outputParams || '[]'
        ) as any[];

        // 如果节点成功并有结果，更新输出参数值
        if (
          status === NODE_STATUS.SUCCESS &&
          result &&
          parsedOutParams.length > 0
        ) {
          // 解析结果数据
          const parsedResult = JSON.parse(result || '{}') as Record<
            string,
            string
          >;

          // 更新参数值
          Object.keys(parsedResult).forEach(paramName => {
            const index = parsedOutParams.findIndex(
              param => param.name === paramName
            );
            if (index > -1) {
              parsedOutParams[index].value = parsedResult[paramName];
            }
          });
        }

        // 更新节点数据
        node.setData({
          ...nodeData,
          status,
          outputParams: JSON.stringify(parsedOutParams),
        });

        // 更新连入该节点的边的样式
        const edges = this.getIncomingEdges(node);
        edges?.forEach(edge => this.updateEdgeStyle(edge, status));
      } catch (error) {
        console.error(`更新节点 ${nodeId} 状态时出错:`, error);
      }
    });
  }

  /**
   * 清除所有节点的状态
   */
  clearStatus(): void {
    // 获取所有节点并重置状态
    const graphNodes = this.getNodes();
    graphNodes.forEach((node: Node) => {
      const data = node.getData();
      node.setData({ ...data, status: null });
    });
  }

  /**
   * 放大画布
   * @param factor - 放大因子，默认为 0.1
   */
  zoomIn(factor: number = 0.1): void {
    const zoom = this.zoom();
    this.zoomTo(zoom + factor);
  }

  /**
   * 缩小画布
   * @param factor - 缩小因子，默认为 0.1
   */
  zoomOut(factor: number = 0.1): void {
    const zoom = this.zoom();
    this.zoomTo(Math.max(0.1, zoom - factor));
  }

  /**
   * 重置画布视图
   * 将画布缩放重置为 1，并居中显示内容
   */
  resetView(): void {
    this.zoomTo(1);
    this.centerContent();
  }
}
