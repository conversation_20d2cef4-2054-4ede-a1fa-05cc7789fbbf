/**
 * @file registerEdges.ts
 * @description 注册审批流程自定义边
 */

import { Graph } from '@antv/x6';

/**
 * 注册审批流程自定义边
 */
export function registerApprovalEdges(): void {
  // 注册审批流程边
  Graph.registerEdge(
    'approval-edge',
    {
      inherit: 'edge', // 继承自基础边
      // 边的 SVG 标记
      markup: [
        {
          tagName: 'path',
          selector: 'wrap',
          attrs: {
            fill: 'none',
            cursor: 'pointer',
            stroke: 'transparent',
            strokeLinecap: 'round',
          },
        },
        {
          tagName: 'path',
          selector: 'line',
          attrs: {
            fill: 'none',
            pointerEvents: 'none',
          },
        },
      ],
      connector: { name: 'normal' }, // 连接器类型
      // 边的属性
      attrs: {
        wrap: {
          connection: true,
          strokeWidth: 10,
          strokeLinejoin: 'round',
        },
        line: {
          connection: true,
          stroke: '#A2B1C3',
          strokeWidth: 2,
          // strokeDasharray: '4 4',
          targetMarker: {
            name: 'block',
            size: 4,
          },
        },
      },
      zIndex: 0, // 层级，确保边在节点下方
    },
    true // 覆盖现有定义
  );
  Graph.registerEdge(
    'normal-edge',
    {
      inherit: 'edge', // 继承自基础边
      // 边的 SVG 标记
      markup: [
        {
          tagName: 'path',
          selector: 'wrap',
          attrs: {
            fill: 'none',
            cursor: 'pointer',
            stroke: 'transparent',
            strokeLinecap: 'round',
          },
        },
        {
          tagName: 'path',
          selector: 'line',
          attrs: {
            fill: 'none',
            pointerEvents: 'none',
          },
        },
      ],
      connector: { name: 'normal' }, // 连接器类型
      // 边的属性
      attrs: {
        wrap: {
          connection: true,
          strokeWidth: 10,
          strokeLinejoin: 'round',
        },
        line: {
          connection: true,
          stroke: '#A2B1C3',
          strokeWidth: 2,
          // strokeDasharray: '4 4',
          targetMarker: {
            name: 'block',
            size: 4,
          },
        },
      },
      zIndex: 0, // 层级，确保边在节点下方
    },
    true // 覆盖现有定义
  );
}
