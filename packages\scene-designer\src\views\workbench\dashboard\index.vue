<template>
  <div class="dashboard">
    <div
      id="dashboard"
      ref="dropTarget"
      :class="['dashboard-container', { 'dashboard-is-active': isDragOver }]"
      :style="workbench.appStyle"
    >
      <KEmpty v-if="!isDragOver && schemaStore.jsonSchema.length === 0" />
      <RenderComponent
        v-for="item in schemaStore.jsonSchema"
        v-else
        :key="item.id"
        :schema="item"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import useSchemaStore from '@/scene-designer/stores/schema';
import useWorkbenchStore from '@/scene-designer/stores/workbench';
import useConfigStore from '@/scene-designer/stores/config';
import KEmpty from '@/scene-designer/components/KEmpty';
import RenderComponent from './RenderComponent.vue';
import { generateUUID } from 'shared/utils';

const schemaStore = useSchemaStore();
const dropTarget = ref<HTMLDivElement>();
const workbench = useWorkbenchStore();
const configStore = useConfigStore();

const isDragOver = ref(false);

onMounted(() => {
  dropTargetForElements({
    element: dropTarget.value!,
    onDragEnter: () => (isDragOver.value = true),
    onDragLeave: () => (isDragOver.value = false),
    onDrop: ({ source, location, self }: any) => {
      isDragOver.value = false;
      workbench.setIsDragging(false);
      if (location.current.dropTargets[0]?.element === self.element) {
        const { item, key } = source.data as any;
        if (item) {
          schemaStore.addSchema(item, item.parentId);
          schemaStore.setCurrentId(item.id);
        } else {
          const id = generateUUID();
          configStore.addPropsConfig(id, key);
          configStore.addStyleConfig(id, key);
          const json: Widget.RenderConfig = {
            id,
            comp: key,
            props: configStore.getFormatedProps(id),
            style: configStore.getFormatedStyle(id),
          };
          schemaStore.setCurrentId(id);
          schemaStore.addSchema(json);
        }
      }
    },
  });
});
</script>
<style lang="less">
.dashboard {
  height: 100%;
  padding: 8px;
  &-container {
    height: 100%;
    overflow-y: auto;
    transition: box-shadow 0.3s ease-in-out;
    box-sizing: border-box;
  }
  .dashboard-is-active {
    box-shadow: rgba(50, 50, 50, 0.25) 6px 6px 12px -2px;
  }
}
</style>
