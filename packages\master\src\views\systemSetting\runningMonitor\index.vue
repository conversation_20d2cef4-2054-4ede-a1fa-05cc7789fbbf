<template>
  <div class="h-full pt-4">
    <div class="bg-[#fff] rounded-lg">
      <div class="flex items-center justify-between p-2">
        <div class="flex items-center gap-5">
          <div>
            名称：
            <a-input
              v-model:value="search.name"
              placeholder="请输入名称"
              style="width: 220px"
            />
          </div>
          <div>
            状态：
            <a-select
              v-model:value="search.status"
              :options="[
                { value: '1', label: '全部' },
                { value: '2', label: '已开始' },
                { value: '3', label: '未开始' },
              ]"
              style="width: 100px"
            />
          </div>
          <a-button type="primary" @click="getTableData">搜索</a-button>
        </div>
        <a-button type="primary" @click="handleAdd">新建</a-button>
      </div>

      <div class="p-2">
        <a-table
          :data-source="tableData"
          :columns
          :scroll="{ y: tableHeight }"
          :pagination="{
            current: page.current,
            pageSize: page.size,
            total: page.total,
            onChange: (current, size) => {
              page.current = current;
              page.size = size;
            },
          }"
          size="small"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { showDialog } from 'dialog-async';
import FormModal from './FormModal.vue';
import { TableColumnsType } from 'ant-design-vue';

const search = ref({
  name: '',
  status: '1',
});

const tableData = ref(
  Array.from({ length: 20 }, (_, i) => ({
    name: 'name' + i,
    status: 'status' + i,
    createTime: new Date().toLocaleString(),
  }))
);
const columns: TableColumnsType<any> = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }) => index + 1,
  },
  { title: '名称', dataIndex: 'name' },
  { title: '状态', dataIndex: 'status' },
  { title: '创建时间', dataIndex: 'createTime' },
  {
    title: '操作',
    key: 'action',
    width: 180,
    customRender: ({ record }) =>
      h('div', {}, [
        h(
          'a-button',
          {
            type: 'link',
            onClick: () => handleEdit(record),
          },
          '编辑'
        ),
        h(
          'a-button',
          {
            type: 'link',
            onClick: () => handleDel(record),
          },
          '删除'
        ),
      ]),
  },
];

const tableHeight = ref(window.innerHeight - 210);

const page = ref({
  current: 1,
  size: 10,
  total: tableData.value.length,
});

const getTableData = () => {
  console.log('get data  :>>', search.value, page.value);
};

const handleAdd = async () => {
  // router.push('/mywork');
  await showDialog(h(<FormModal isNew={true} />));
};

const handleEdit = (row: any) => {
  console.log('edit  :>>', row);
};

const handleDel = (row: any) => {
  console.log('del  :>>', row);
};

onMounted(() => {
  window.addEventListener('resize', () => {
    tableHeight.value = window.innerHeight - 210;
  });
});
</script>

<style lang="less" scoped></style>
