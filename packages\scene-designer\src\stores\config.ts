import { formatConfig } from '../utils';
import 'core-js/actual/structured-clone';

export default defineStore('widgetConfig', {
  state: () => ({
    baseStyleConfig: new Map<string, Widget.StyleConfig>(),
    basePropsConfig: new Map<string, Widget.PropsConfig>(),
    styleConfig: new Map<string, Widget.StyleConfig>(),
    propsConfig: new Map<string, Widget.PropsConfig>(),
  }),
  actions: {
    initStyleConfig() {
      const defaultStyleConfig = import.meta.glob<Widget.PropsConfig>(
        '@/scene-designer/components/**/style.ts',
        {
          eager: true,
          import: 'default',
        }
      );
      Object.keys(defaultStyleConfig).forEach(key => {
        const componentName = key
          .split('components/')[1]
          .replace('/style.ts', '');
        this.baseStyleConfig.set(componentName, defaultStyleConfig[key]);
      });
    },
    initPropsConfig(injectProps?: Widget.PropsConfig) {
      const defaultPropsConfigMap = import.meta.glob<Widget.PropsConfig>(
        '@/scene-designer/components/**/props.ts',
        {
          eager: true,
          import: 'default',
        }
      );
      const isInputComp = (compName: string) =>
        [
          'KInput',
          'KSelect',
          'KRadio',
          'KCheckbox',
          'KRangePicker',
          'KSwitch',
          'KText',
        ].includes(compName);
      Object.keys(defaultPropsConfigMap).forEach(key => {
        const componentName = key
          .split('components/')[1]
          .replace('/props.ts', '');
        let defaultPropsConfig = defaultPropsConfigMap[key];
        if (injectProps && isInputComp(componentName)) {
          defaultPropsConfig = {
            ...injectProps,
            ...defaultPropsConfig,
          };
        }
        this.basePropsConfig.set(componentName, defaultPropsConfig);
      });
    },
    addStyleConfig(id: string, key: string) {
      const config = structuredClone(toRaw(this.baseStyleConfig.get(key)));
      this.styleConfig.set(id, config!);
    },
    addPropsConfig(id: string, key: string) {
      const config = structuredClone(toRaw(this.basePropsConfig.get(key)));
      this.propsConfig.set(id, config!);
    },
    getFormatedStyle(componentId: string) {
      const config = this.styleConfig.get(componentId);
      return config ? formatConfig(config) : {};
    },
    getFormatedProps(componentId: string) {
      const config = this.propsConfig.get(componentId);
      return config ? formatConfig(config) : {};
    },
  },
});
