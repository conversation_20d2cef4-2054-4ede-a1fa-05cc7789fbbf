<template>
  <a-card title="工具库管理" class="toolbox-container">
    <template #extra>
      <div class="toolbox-header-actions">
        <a-space>
          <!-- 视图切换按钮 -->
          <a-tooltip title="表格视图">
            <MenuOutlined
              class="view-toggle-btn"
              :class="{ active: viewMode === 'table' }"
              @click="handleViewModeChange('table')"
            />
          </a-tooltip>
          <a-tooltip title="卡片视图">
            <MingcuteDotGridFill
              class="view-toggle-btn"
              :class="{ active: viewMode === 'card' }"
              @click="handleViewModeChange('card')"
            />
          </a-tooltip>

          <!-- 操作按钮 -->
          <a-button
            type="primary"
            @click="handleAddSoftwareComponentWithReload"
          >
            <GridiconsAdd class="mr-1" />
            新增专业软件组件
          </a-button>
          <a-button
            type="primary"
            ghost
            @click="handleAddBasicComponentWithReload"
          >
            <GridiconsAdd class="mr-1" />
            新增通用组件
          </a-button>
        </a-space>
      </div>
    </template>

    <!-- 内容区域 -->
    <div class="toolbox-content">
      <TableList v-if="viewMode === 'table'" @reload="handleReload" />
      <div v-else class="card-view-container">
        <a-spin :spinning="loading">
          <CardList :data-source="data" @reload="handleReload" />
        </a-spin>
      </div>
    </div>
  </a-card>
</template>
<script lang="tsx" setup>
import CardList from './components/cardList.vue';
import TableList from './components/TableList.vue';
import { useToolbox, useToolActions } from './composables/useToolbox';
import MenuOutlined from '~icons/ant-design/menu-outlined';
import MingcuteDotGridFill from '~icons/mingcute/dot-grid-fill';
import GridiconsAdd from '~icons/gridicons/add';

// 使用工具箱逻辑
const { viewMode, data, loading, handleViewModeChange, handleReload } =
  useToolbox();

// 使用工具操作逻辑
const { handleAddSoftwareComponent, handleAddBasicComponent } =
  useToolActions();

// 增强的事件处理函数
const handleAddSoftwareComponentWithReload = async () => {
  const success = await handleAddSoftwareComponent();
  if (success) {
    handleReload();
  }
};

const handleAddBasicComponentWithReload = async () => {
  const success = await handleAddBasicComponent();
  if (success) {
    handleReload();
  }
};
</script>

<style lang="less" scoped>
.toolbox-container {
  height: 100%;

  .toolbox-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .view-toggle-btn {
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    color: rgba(0, 0, 0, 0.45);

    &:hover {
      color: var(--primary-color);
      background-color: rgba(0, 0, 0, 0.04);
    }

    &.active {
      color: var(--primary-color);
      background-color: rgba(24, 144, 255, 0.1);
    }
  }

  .toolbox-content {
    height: calc(100% - 60px);
    overflow: hidden;

    .card-view-container {
      height: 100%;
      overflow-y: auto;
    }
  }
}
</style>
