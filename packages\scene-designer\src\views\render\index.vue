<template>
  <div
    style="padding: 8px; height: 100%; box-sizing: border-box"
    :style="workbenchStore.appStyle"
  >
    <a-spin :spinning="loading" style="height: 100%">
      <RenderComponent
        v-for="item in schemaStore.jsonSchema"
        :key="item.id"
        :schema="item"
        :value-change="handleValueChange"
      />
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { utils } from '@kd/utils';
import RenderComponent from './RenderComponent.vue';
import useSchemaStore from '@/scene-designer/stores/schema';
import useWorkbenchStore from '@/scene-designer/stores/workbench';
import components from '@/scene-designer/components/index';
import useBindingStore from '@/scene-designer/stores/binding';

const { schema, initSchema } = defineProps<{
  schema?: Widget.RenderConfig[];
  initSchema?: (...args: any) => Promise<Widget.RenderConfig[]>;
}>();

const emits = defineEmits<{
  valueChange: [e: Widget.RenderConfig[]];
}>();

const schemaStore = useSchemaStore();
const workbenchStore = useWorkbenchStore();
const bindingStore = useBindingStore();

const loading = ref(true);

const initComponent = () => {
  const currentInstance = getCurrentInstance();
  const appInstance = currentInstance?.appContext.app;
  if (appInstance) {
    appInstance.use(components);
  }
};

onMounted(async () => {
  workbenchStore.setMode('preview');
  schemaStore.initSchema(schema || []);
  initComponent();
  if (initSchema && utils.isPromise(initSchema)) {
    const json = await initSchema();
    schemaStore.initSchema(json);
  } else if (schema?.length) {
    schemaStore.initSchema(schema);
  }
  loading.value = false;

  // window.addEventListener('drop', (e: any) => {
  //   const inputNodes = ['input', 'select'];
  //   const data = JSON.parse(sessionStorage.getItem('dragData') || '{}');
  //   if (!e.target) return;
  //   if (!inputNodes.includes(e.target.nodeName.toLowerCase())) return;
  //   const { id } = e.target;
  //   if (id) {
  //     const currentSchema = schemaStore.getSchemaById(id);
  //     setValue(id, data.name);
  //     emits('drop', { node: currentSchema!, data });
  //   }
  // });
});

const setValue = (id: string, value: any) => {
  const currentSchema = schemaStore.getSchemaById(id);
  if (currentSchema) {
    currentSchema.props.value = value;
  }
};

const valueMap = new Map<string, Widget.RenderConfig>();

const handleValueChange = (node: Widget.RenderConfig) => {
  valueMap.set(node.id, node);
  emits('valueChange', Array.from(valueMap.values()));
};

const getBindingMap = () => {
  return Array.from(bindingStore.bindingMap.values());
};

defineExpose({
  setValue,
  getBindingMap,
});
</script>
