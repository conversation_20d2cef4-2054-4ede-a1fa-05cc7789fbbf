<template>
  <div
    class="tool-item"
    :style="{
      opacity: isDragging ? 0.5 : 1,
    }"
  >
    <div
      class="tool-item__icon"
      draggable="true"
      @dragstart="handleDragStart"
      @dragend="handleDragEnd"
    >
      <div v-if="isObjectString" class="tool-item__icon-img">
        <icon :value="parsedIcon" />
      </div>
      <component
        v-else
        :is="getIcon(item.componentIcon)"
        class="tool-item__icon-svg"
      />
    </div>
    <a-tooltip :title="item.componentName">
      <p class="tool-item__label">{{ item.componentName }}</p>
    </a-tooltip>
  </div>
</template>

<script lang="ts" setup>
/**
 * @file ToolItem.vue
 * @description 工具项组件，用于展示单个可拖拽的工具
 */
import { getIcon } from '@/master/views/toolbox/icons';
import type { Tool } from '@/master/types/tool';
import { useWorkflowGraph } from '../flow/useWorkFlowGraph';
import Icon from 'shared/components/Icon/index.vue';
/**
 * 组件属性定义
 */
const { item } = defineProps<{
  /** 工具项数据 */
  item: Tool;
}>();

const parsedIcon = computed(() => JSON.parse(item.componentIcon));

const isObjectString = computed(() => item.componentIcon.startsWith('{'));

/**
 * 拖拽状态
 */
const isDragging = ref(false);

/**
 * 工作流图实例
 */
const { workflow } = useWorkflowGraph();

/**
 * 处理拖拽开始事件
 * @param {DragEvent} event - 拖拽事件对象
 */
const handleDragStart = (event: DragEvent): void => {
  isDragging.value = true;
  workflow.value?.cleanSelection();

  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy';
    event.dataTransfer.setData('application/json', JSON.stringify(item));
  }
};

/**
 * 处理拖拽结束事件
 * @param {DragEvent} event - 拖拽事件对象
 */
const handleDragEnd = (event: DragEvent): void => {
  event.preventDefault();
  isDragging.value = false;
};
</script>

<style lang="less" scoped>
.tool-item {
  color: var(--primary-100);

  &__icon {
    display: flex;
    cursor: grab;
    height: 80px;
    width: 80px;
    margin: 0 auto;
    background-color: var(--bg-200);
    border-radius: 4px;
    border: 1px solid var(--bg-100);
    transition: 0.15s ease-in-out;
    transition-property: border-color;
    box-sizing: border-box;

    &:hover {
      border-color: var(--bg-300);
    }
    &-img {
      width: 48px;
      height: 48px;
      margin: auto;
    }

    &-svg {
      width: 48px;
      height: 48px;
      margin: auto;
    }
  }

  &__label {
    white-space: nowrap;
    font-size: 14px;
    line-height: 12px;
    margin: 4px -2px 0;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-200);
    padding: 4px 0;
  }
}
</style>
