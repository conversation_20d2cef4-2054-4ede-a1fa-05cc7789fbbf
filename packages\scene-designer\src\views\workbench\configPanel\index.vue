<template>
  <div class="config-panel">
    <div class="config-panel__header">
      <h5 class="config-panel__title">
        <UilSetting />
        <span>配置面板</span>
      </h5>
    </div>
    <div class="config-panel__content">
      <a-form :label-col="{ style: { width: '80px' } }">
        <a-collapse
          v-model:active-key="activeKey"
          :bordered="false"
          ghost
          expand-icon-position="end"
        >
          <!-- 全局配置区域 -->
          <config-panel-section
            v-if="!schemaStore.currentId"
            title="全局配置"
            :icon="HugeIconsPropertyEdit"
            :model="appStyleModel"
            :on-change="handleAppStyleChange"
          />

          <!-- 属性配置区域 -->
          <config-panel-section
            title="属性配置"
            :icon="HugeIconsPropertyEdit"
            :model="propsModel"
            :on-change="handlePropsValueChange"
            :show-empty-message="true"
          />

          <!-- 样式配置区域 -->
          <config-panel-section
            title="样式配置"
            :icon="ProIconsTextEditStyle"
            :model="styleModel"
            :on-change="handleStyleValueChange"
            :show-empty-message="true"
          />
        </a-collapse>
      </a-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import useSchemaStore from '@/scene-designer/stores/schema';
import useWorkBenchStore from '@/scene-designer/stores/workbench';
import useConfigStore from '@/scene-designer/stores/config';
import useBindingStore from '@/scene-designer/stores/binding';
import HugeIconsPropertyEdit from '~icons/hugeicons/property-edit';
import ProIconsTextEditStyle from '~icons/proicons/text-edit-style';
import UilSetting from '~icons/uil/setting';
import ConfigPanelSection from './components/ConfigPanelSection.vue';
import { extractValue } from './utils';
// 获取状态管理
const schemaStore = useSchemaStore();
const workBenchStore = useWorkBenchStore();
const configStore = useConfigStore();
const bindingStore = useBindingStore();

const injectPropsChange =
  inject<
    (params: {
      value: string;
      renderSchema: Widget.RenderConfig;
      configSchema: Widget.PropsConfig;
      option?: Global.Option;
    }) => void
  >('injectPropsChange');

// 面板状态
const activeKey = ref(['0', '1', '2']);

// 配置模型
const propsModel = ref({} as Widget.PropsConfig);
const styleModel = ref({} as Widget.StyleConfig);
const appStyleModel = ref(workBenchStore.appStyleSchema);

// 当前选中的组件
const schema = computed(() => schemaStore.currentSchema);

/**
 * 监听当前选中组件变化，更新配置模型
 */
watchEffect(() => {
  if (schema.value) {
    propsModel.value = configStore.propsConfig.get(schema.value.id)!;
    styleModel.value = configStore.styleConfig.get(schema.value.id)!;
  }
});

/**
 * 处理属性值变更
 * @param key 属性键名
 * @param e 事件或值
 */
const handlePropsValueChange = ({
  key,
  value,
  option,
  isInjectProps,
}: {
  key: string;
  value: any;
  option?: Global.Option;
  isInjectProps?: boolean;
}) => {
  const extractedValue = extractValue(value);
  if (schemaStore.currentSchema) {
    schemaStore.currentSchema.props[key] = extractedValue;
    // 特殊处理绑定字段
    if (isInjectProps) {
      bindingStore.setBinding(schemaStore.currentSchema);
      injectPropsChange &&
        injectPropsChange({
          value: extractedValue,
          option,
          renderSchema: schemaStore.currentSchema,
          configSchema: propsModel.value,
        });
    }
  }
};

/**
 * 处理样式值变更
 * @param key 样式键名
 * @param e 事件或值
 */
const handleStyleValueChange = ({
  key,
  value,
}: {
  key: string;
  value: any;
  options?: Global.Option;
  isInjectProps?: boolean;
}) => {
  const schema = schemaStore.getSchemaById(schemaStore.currentId);
  if (schema) {
    schema.style[key] = extractValue(value);
  }
};

/**
 * 处理应用样式变更
 * @param key 样式键名
 * @param e 事件或值
 */
const handleAppStyleChange = ({ key, value }: { key: string; value: any }) => {
  workBenchStore.setAppStyle(key, extractValue(value));
};
</script>
<style lang="less">
.config-panel {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #dedede;
    margin-bottom: 12px;
    padding: 4px 8px;
  }

  &__title {
    display: flex;
    align-items: center;
    color: #666;
    font-weight: bold;
    padding: 8px 0;
    font-size: 14px;

    span {
      margin-left: 4px;
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 0 8px;
  }
}
</style>
