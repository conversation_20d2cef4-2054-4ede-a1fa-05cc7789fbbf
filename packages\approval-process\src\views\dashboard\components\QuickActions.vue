<template>
  <a-card :title="title" class="col-span-3 h-[120px]">
    <div class="flex justify-around">
      <div
        v-for="action in actions"
        :key="action.key"
        class="text-center cursor-pointer"
        @click="$emit('action', action.key)"
      >
        <div
          class="action-icon-wrapper"
          :class="[`bg-${action.color}-100 text-${action.color}-500`]"
        >
          <component :is="action.icon" class="text-xl" />
        </div>
        <div class="mt-2">{{ action.title }}</div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
defineProps<{
  title: string;
  actions: {
    key: string;
    title: string;
    icon: any;
    color: string;
  }[];
}>();

defineEmits<{
  (e: 'action', key: string): void;
}>();
</script>
<style>
.action-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-icon-wrapper:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}
</style>
