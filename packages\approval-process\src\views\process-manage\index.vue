<template>
  <div class="h-full p-2">
    <!-- 搜索表单和操作按钮 -->
    <div class="mb-4 flex justify-between items-center">
      <a-form layout="inline">
        <a-form-item label="流程名称">
          <a-input
            v-model:value="searchParams.name"
            placeholder="请输入流程名称"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchParams.status"
            :options="statusOptions"
            style="width: 120px"
            placeholder="请选择状态"
            allow-clear
          />
        </a-form-item>
        <a-form-item label="申请时间">
          <a-range-picker
            v-model:value="dateRange"
            :show-time="{ format: 'HH:mm' }"
            format="YYYY-MM-DD HH:mm"
            @change="handleDateRangeChange"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 新增流程按钮 -->
      <a-button type="primary" @click="handleAdd">
        <template #icon>
          <PlusOutlined />
        </template>
        新增流程
      </a-button>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :pagination="pagination"
      :loading="loading"
      row-key="id"
      @change="handleTableChange"
    >
      <!-- 状态列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <!-- 操作列 -->
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button
              type="link"
              size="small"
              @click="handleEdit(record as Process)"
            >
              编辑
            </a-button>
            <a-button
              type="link"
              size="small"
              @click="handleView(record as Process)"
            >
              查看
            </a-button>
            <a-popconfirm
              title="确定要删除该流程吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record as Process)"
            >
              <a-button type="link" size="small" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import type { TablePaginationConfig, TableProps } from 'ant-design-vue';
import PlusOutlined from '~icons/ant-design/plus-outlined';
import { getStatusText, getStatusColor, getStatusOptions } from './utils';
import type { Process, ProcessQueryParams } from '../../types/process';
import { mockGetProcessList, mockDeleteProcess } from '../../apis/mock-process';

// 表格列定义
const columns = [
  {
    title: '流程名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
    sorter: true,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
    sorter: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: getStatusOptions()
      .filter(item => item.value !== '')
      .map(item => ({ text: item.label, value: item.value })),
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right' as const,
  },
];

// 状态选项
const statusOptions = getStatusOptions();

// 路由
const router = useRouter();

// 搜索参数
const searchParams = ref<ProcessQueryParams>({
  name: '',
  status: undefined,
  startTime: undefined,
  endTime: undefined,
  pageNum: 1,
  pageSize: 10,
});

// 日期范围
const dateRange = ref<any>(null);

// 表格数据
const tableData = ref<Process[]>([]);

// 加载状态
const loading = ref<boolean>(false);

// 分页配置
const pagination = reactive<TablePaginationConfig>({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: total => `共 ${total} 条`,
});

// 日期范围变化处理
const handleDateRangeChange = (value: any, dateStrings: [string, string]) => {
  if (value) {
    searchParams.value.startTime = dateStrings[0];
    searchParams.value.endTime = dateStrings[1];
  } else {
    searchParams.value.startTime = undefined;
    searchParams.value.endTime = undefined;
  }
};

// 搜索处理
const handleSearch = () => {
  searchParams.value.pageNum = 1;
  fetchData();
};

// 重置处理
const handleReset = () => {
  searchParams.value = {
    name: '',
    status: undefined,
    startTime: undefined,
    endTime: undefined,
    pageNum: 1,
    pageSize: 10,
  };
  dateRange.value = null;
  fetchData();
};

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (
  pag,
  filters,
  sorter: any
) => {
  // 处理分页
  pagination.current = pag?.current || 1;
  pagination.pageSize = pag?.pageSize || 10;
  searchParams.value.pageNum = pagination.current;
  searchParams.value.pageSize = pagination.pageSize;

  // 处理筛选
  if (filters.status && filters.status.length > 0) {
    searchParams.value.status = filters.status[0] as any;
  } else {
    searchParams.value.status = undefined;
  }

  // 处理排序
  if (sorter.field && sorter.order) {
    // 这里可以根据需要添加排序逻辑
  }

  fetchData();
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const res = await mockGetProcessList(searchParams.value);
    tableData.value = res.records;
    pagination.total = res.total;
    pagination.current = res.current;
  } catch (error: any) {
    message.error(error.message || '获取流程列表失败');
  } finally {
    loading.value = false;
  }
};

// 新增流程
const handleAdd = () => {
  router.push('/process-design');
};

// 编辑流程
const handleEdit = (record: Process) => {
  router.push({
    path: '/process-design',
    query: { id: record.id },
  });
};

// 查看流程
const handleView = (record: Process) => {
  router.push({
    path: '/process-design',
    query: { id: record.id, mode: 'view' },
  });
};

// 删除流程
const handleDelete = async (record: Process) => {
  try {
    await mockDeleteProcess([record.id]);
    message.success('删除成功');
    fetchData();
  } catch (error: any) {
    message.error(error.message || '删除失败');
  }
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>
