<template>
  <component :is="isContentMode ? ContentMode : NormalMode" v-bind="props">
    <template v-for="(_, name) in $slots" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps"></slot>
    </template>
  </component>
</template>
<script lang="ts" setup>
import { eventEmitter } from '@kd/utils';
import ContentMode from './ContentMode.vue';
import NormalMode from './NormalMode.vue';
import { message } from 'ant-design-vue';

const props = defineProps({
  showSider: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: import.meta.env.VITE_APP_TITLE,
  },
  showBreadcrumb: {
    type: Boolean,
    default: false,
  },
});

const isContentMode = computed(() =>
  window.location.search.includes('contentMode')
);

eventEmitter.on('API_ERROR', e => {
  e && message.error(e.message);
});
</script>
