<template>
  <a-sub-menu
    v-if="
      item.children && item.children.length > 0 && item.meta?.showMenu !== false
    "
    :key="resolvedBasePath"
    :title="item.meta?.title"
  >
    <template #icon>
      <component :is="item.meta!.icon as string" />
    </template>
    <sidebar-item
      v-for="child in item.children"
      :key="child.path"
      :item="child"
      :base-path="resolvedBasePath"
    />
  </a-sub-menu>
  <a-menu-item
    v-else-if="item.meta?.showMenu !== false"
    :key="resolvedBasePath"
  >
    <template #icon>
      <component :is="item.meta!.icon as string" />
    </template>
    <Link :to="resolvedBasePath">
      {{ item.meta?.title }}
    </Link>
  </a-menu-item>
</template>
<script lang="ts" setup>
import type { RouteRecordRaw } from 'vue-router';
import { utils } from '@kd/utils';
import Link from './Link.vue';

/**
 * @description 递归生成菜单
 * @props item: 当前路由信息 basePath: 递归前的路由path
 */
// 定义props
const props = defineProps<{
  item: RouteRecordRaw;
  basePath: string;
}>();
// 解构出basePath

const formatPath = (path: string) => (path.startsWith('/') ? path : `/${path}`);

const resolvedBasePath = computed(() => {
  if (utils.isExternal(props.basePath)) return props.basePath;
  return `${props.basePath === '/' ? '' : props.basePath}${formatPath(props.item.path)}`;
});
</script>
