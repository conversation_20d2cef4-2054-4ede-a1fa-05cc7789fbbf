<template>
  <div class="h-full">
    <div class="w-1/2 mx-auto">
      <a-card title="基础信息">
        <a-form
          ref="formRef"
          :model="toolParams"
          :label-col="{ style: 'width: 80px' }"
        >
          <a-form-item label="组件名称" name="componentName" required>
            <a-input
              v-model:value="toolParams.componentName"
              placeholder="请输入组件名称"
            />
          </a-form-item>
          <a-form-item label="分类" name="sysDesComponentTypeId" required>
            <a-select
              v-model:value="toolParams.sysDesComponentTypeId"
              placeholder="请选择分类"
              :options
            >
            </a-select>
          </a-form-item>
          <a-form-item label="超时时间" name="timeoutDuration" required>
            <a-input-number
              v-model:value="toolParams.timeoutDuration"
              class="w-full"
            >
              <template #addonAfter>
                <span>秒</span>
              </template>
            </a-input-number>
          </a-form-item>
          <a-form-item label="描述" name="remarks">
            <a-textarea
              v-model:value="toolParams.remarks"
              placeholder="请输入组件描述信息"
              :auto-size="{ minRows: 3, maxRows: 6 }"
            />
          </a-form-item>
          <a-form-item label="图标" name="componentIcon" required>
            <IconMaker v-model:value="toolParams.componentIcon" />
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getToolCategory } from '@/master/apis/tools';
import { useToolContext } from '../../useTool';
import { useRequest } from 'vue-hooks-plus';
import { FormInstance } from 'ant-design-vue';
import IconMaker from 'shared/components/IconMaker/index.vue';

const { toolParams } = useToolContext();
const formRef = useTemplateRef<FormInstance>('formRef');
const { data: options } = useRequest(getToolCategory, {
  defaultParams: [{ current: 1, size: 100, child: false }],
  formatResult: res =>
    res.records.map(t => ({
      label: t.name,
      value: t.sysDesComponentTypeId,
    })),
});
const validate = () => {
  return formRef.value?.validateFields();
};

defineExpose({
  validate,
});
</script>
