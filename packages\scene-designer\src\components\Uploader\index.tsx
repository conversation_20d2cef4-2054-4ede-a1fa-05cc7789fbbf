import { UploadDragger } from 'ant-design-vue';
import InboxOutlined from '~icons/ant-design/inbox-outlined';

export default () => {
  const fileList = ref([]);

  const handleChange = () => {};

  const handleDrop = () => {};

  return (
    <UploadDragger
      v-model:fileList={fileList.value}
      name="file"
      multiple={true}
      action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
      onChange={handleChange}
      onDrop={handleDrop}
    >
      <p class="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p class="ant-upload-text">点击或将文件拖到此处，上传</p>
      <p class="ant-upload-hint">
        支持单个或批量上传。严禁上传公司数据或其他违禁文件
      </p>
    </UploadDragger>
  );
};
