import { request } from '@kd/utils';
import type { UploadModelImageResponse } from '../types/model-management';

/**
 * 上传模型图片
 * @param file 图片文件
 * @param sysDesModelAttachmentId 模型版本控制表ID
 * @returns 上传结果
 */
export const uploadModelImage = (file: File, sysDesModelId: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sysDesModelId', sysDesModelId);

  return request<UploadModelImageResponse>({
    url: '/component/sysDesModel/uploadModelImage',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 上传单文件模型
 * @param file 模型文件
 * @param sysDesModelAttachmentId 模型版本控制表ID
 * @param sysDesModelServerId 专业软件服务器配置ID
 * @param fileVersion 版本号
 * @returns 上传结果
 */
export const uploadModel = (data: {
  file: File;
  sysDesModelId: string;
  desProfessionalSoftwareId: string;
}) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('sysDesModelId', data.sysDesModelId);
  formData.append('desProfessionalSoftwareId', data.desProfessionalSoftwareId);
  return request<string>({
    url: '/component/sysDesModel/uploadModel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};
