<template>
  <div class="widget-panel">
    <a-collapse
      v-model:active-key="activeKey"
      ghost
      :bordered="false"
      expand-icon-position="end"
    >
      <a-collapse-panel v-for="(resource, key) in resources" :key>
        <template #header>
          <span class="widget-title">{{ resource.title }}</span>
        </template>
        <div class="widget-container">
          <WidgetItem v-for="item in resource.widgets" :key="item.key" :item />
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<script setup lang="ts">
import resources from './resources';
import WidgetItem from './WidgetItem.vue';

const activeKey = ref(Object.keys(resources));
</script>
<style lang="less">
.widget-panel {
  height: 100%;
  overflow-y: auto;
}
.widget-title {
  font-weight: bold;
  color: var(--text-100);
}
.widget-container {
  display: grid;
  text-align: center;
  grid-template-columns: repeat(auto-fill, minmax(54px, 1fr));
  gap: 8px;
}
</style>
