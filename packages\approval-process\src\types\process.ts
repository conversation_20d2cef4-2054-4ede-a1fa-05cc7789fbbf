/**
 * 流程状态枚举
 */
export enum ProcessStatus {
  /** 草稿 */
  DRAFT = 'draft',
  /** 已发布 */
  PUBLISHED = 'published',
  /** 已禁用 */
  DISABLED = 'disabled',
}

/**
 * 流程实体接口
 */
export interface Process {
  /** 流程ID */
  id: string;
  /** 流程名称 */
  name: string;
  /** 流程描述 */
  description: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
  /** 状态 */
  status: ProcessStatus;
}

/**
 * 流程图标接口
 */
export interface ProcessIcon {
  /** 图标类型: 'text' | 'image' */
  type: 'text' | 'image' | 'svg';
  /** 图标内容: 文本或图片URL */
  content: string;
  /** 图标背景色 */
  backgroundColor?: string;
  /** 图标文本颜色 */
  color?: string;
}

/**
 * 流程表单接口
 */
export interface ProcessForm {
  /** 表单结构 */
  structure: any[];
  /** 表单数据 */
  data: Record<string, any>;
}

/**
 * 流程图接口
 */
export interface ProcessFlow {
  /** 流程图节点 */
  nodes: any[];
  /** 流程图连线 */
  edges: any[];
}

/**
 * 流程设计数据接口
 */
export interface ProcessDesignData extends Partial<Process> {
  /** 流程图标 */
  icon: ProcessIcon;
  /** 流程分组 */
  group: string;
  /** 表单设计数据 */
  form: ProcessForm;
  /** 流程设计数据 */
  flow: ProcessFlow;
  /** 更多设置 */
  settings: Record<string, any>;
  /** 最近保存时间 */
  lastSavedTime?: string;
}

/**
 * 流程查询参数接口
 */
export interface ProcessQueryParams extends Global.Pagination {
  /** 流程名称 */
  name?: string;
  /** 状态 */
  status?: ProcessStatus;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}
