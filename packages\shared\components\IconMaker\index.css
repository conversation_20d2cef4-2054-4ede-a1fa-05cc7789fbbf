.icon-select {
  display: inline-block;
  cursor: pointer;
}

.text-icon {
  width: 100%;
  height: 100%;
  text-align: center;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
}

.image-icon {
  width: 100%;
  height: 100%;
}

.image-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.placeholder-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
  line-height: 1.2;
  height: 100%;
}

.image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-preview {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-crop-area {
  margin-top: 20px;
  width: 100%;
}

.crop-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.cropper-container {
  width: 100%;
  height: 300px;
  margin: 0 auto;
  background-color: #f5f5f5;
  overflow: hidden;
  position: relative;
}

.preview-container {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.preview-circle {
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
}

.preview-circle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* 圆形裁剪区域样式 */
.circle-stencil {
  border-radius: 50%;
}

.vue-advanced-cropper__stencil-wrapper {
  border-radius: 50%;
}
