<template>
  <template v-if="schema.children && schema.children.length > 0">
    <component
      :is="schema.comp"
      :id="schema.id"
      v-bind="schema.props"
      :style="schema.style"
    >
      <RenderComponent
        v-for="child in schema.children"
        :key="child.id"
        :schema="child"
        :value-change
      />
    </component>
  </template>
  <component
    :is="schema.comp"
    v-else
    :id="schema.id"
    v-bind="schema.props"
    :style="schema.style"
    @change.self="(e: any) => handleChange(e, schema)"
  />
</template>
<script lang="ts" setup>
const { schema, valueChange } = defineProps<{
  schema: Widget.RenderConfig;
  valueChange: (e: Widget.RenderConfig) => void;
}>();

const handleChange = (e: any, json: Widget.RenderConfig) => {
  const value = e.target ? e.target.value : e;
  if (!json.props) json.props = {};
  json.props.value = value;
  valueChange && valueChange(json);
};
</script>
