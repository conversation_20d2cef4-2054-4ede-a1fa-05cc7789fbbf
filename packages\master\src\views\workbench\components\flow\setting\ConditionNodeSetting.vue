<template>
  <a-form :label-col="{ style: 'width: 80px' }">
    <!-- 节点名称输入框 -->
    <a-form-item label="节点名称">
      <a-input
        v-model:value="formState.componentName"
        placeholder="请输入节点名称"
        @change="handleChange"
      />
    </a-form-item>
    <a-form-item label="条件项" required>
      <div
        v-for="item in formState.conditions"
        :key="item.id"
        class="flex items-center gap-2 mb-4"
      >
        <span>当：</span>
        <a-input
          v-model:value="item.condition"
          placeholder="请输入条件表达式"
          @change="handleChange"
        />
        <a-tooltip title="删除">
          <MinusCircleOutlined
            class="cursor-pointer"
            @click="handleDeleteCondition(item.id)"
          />
        </a-tooltip>
      </div>
      <a-button block type="dashed" @click="handleAddCondition">
        <PlusOutlined />
      </a-button>
    </a-form-item>

    <!-- 描述文本框 -->
    <a-form-item label="描述">
      <a-textarea
        v-model:value="formState.description"
        :rows="3"
        placeholder="请输入节点描述"
        @change="handleChange"
      />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import type { Node } from '@antv/x6';
import PlusOutlined from '~icons/ant-design/plus-outlined';
import MinusCircleOutlined from '~icons/ant-design/minus-circle-outlined';
import { GROUP_NODE_WIDTH } from '../../../constants';
import { generateUUID } from 'shared/utils';
import type { ConditionNodeData } from '@/master/types/flow';

const props = defineProps<{
  node: Node;
}>();

// 表单状态
const formState = ref(props.node.data as ConditionNodeData);

/**
 * 添加条件节点的连接桩
 */
const addConditionPort = (id: string) => {
  const length = formState.value.conditions.length;
  // 计算每个条件项之间的间距
  const spacing = 62;
  // 重新创建连接桩
  props.node.addPort({
    id,
    group: 'right',
    args: {
      x: GROUP_NODE_WIDTH + 1,
      y: spacing + 38 * (length - 1),
    },
  });
};

const deleteConditionPort = (id: string) => {
  props.node.removePort(id);
};

const changeNodeSize = (isAdd: boolean) => {
  const conditionItemHeight = 38;
  if (props.node) {
    const originSize = props.node.getSize();
    const height = isAdd
      ? originSize.height + conditionItemHeight
      : originSize.height - conditionItemHeight;
    props.node.prop({
      size: { width: originSize.width, height },
    });
  }
};

const handleChange = () => {
  if (props.node) {
    const currentData = props.node.getData();
    props.node.setData({
      ...currentData,
      componentName: formState.value.componentName,
      description: formState.value.description,
      conditions: formState.value.conditions,
    });
  }
};

// 添加条件项
const handleAddCondition = () => {
  const item = {
    condition: '',
    id: generateUUID(),
  };
  formState.value.conditions.push(item);
  handleChange();
  changeNodeSize(true);
  // 数据变化时更新端口
  addConditionPort(item.id);
};

// 删除条件项
const handleDeleteCondition = (id: string) => {
  formState.value.conditions = formState.value.conditions.filter(
    t => t.id !== id
  );
  handleChange();
  changeNodeSize(false);
  deleteConditionPort(id);
};
</script>
