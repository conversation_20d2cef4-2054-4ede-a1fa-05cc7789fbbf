import type { Process, ProcessQueryParams, ProcessStatus } from '../types/process';
import dayjs from 'dayjs';

// 模拟数据
const mockProcesses: Process[] = [
  {
    id: '1',
    name: '采购申请流程',
    description: '用于处理公司内部采购申请的审批流程',
    creator: '张三',
    createTime: '2023-11-01 10:30:00',
    updateTime: '2023-11-10 15:45:00',
    status: 'published' as ProcessStatus,
  },
  {
    id: '2',
    name: '请假申请流程',
    description: '员工请假申请的审批流程',
    creator: '李四',
    createTime: '2023-10-15 09:20:00',
    updateTime: '2023-11-05 11:30:00',
    status: 'published' as ProcessStatus,
  },
  {
    id: '3',
    name: '报销申请流程',
    description: '员工报销申请的审批流程',
    creator: '王五',
    createTime: '2023-09-20 14:15:00',
    updateTime: '2023-10-25 16:40:00',
    status: 'draft' as ProcessStatus,
  },
  {
    id: '4',
    name: '出差申请流程',
    description: '员工出差申请的审批流程',
    creator: '赵六',
    createTime: '2023-08-10 11:25:00',
    updateTime: '2023-09-15 10:20:00',
    status: 'disabled' as ProcessStatus,
  },
  {
    id: '5',
    name: '加班申请流程',
    description: '员工加班申请的审批流程',
    creator: '钱七',
    createTime: '2023-07-05 16:50:00',
    updateTime: '2023-08-20 09:10:00',
    status: 'published' as ProcessStatus,
  },
];

// 模拟获取流程列表
export const mockGetProcessList = (
  params: ProcessQueryParams
): Promise<Global.ListResponse<Process>> => {
  let filteredData = [...mockProcesses];

  // 按名称筛选
  if (params.name) {
    filteredData = filteredData.filter(item =>
      item.name.includes(params.name || '')
    );
  }

  // 按状态筛选
  if (params.status) {
    filteredData = filteredData.filter(item => item.status === params.status);
  }

  // 按时间范围筛选
  if (params.startTime && params.endTime) {
    filteredData = filteredData.filter(
      item =>
        dayjs(item.createTime).isAfter(dayjs(params.startTime)) &&
        dayjs(item.createTime).isBefore(dayjs(params.endTime))
    );
  }

  // 计算分页
  const total = filteredData.length;
  const start = (params.pageNum - 1) * params.pageSize;
  const end = start + params.pageSize;
  const records = filteredData.slice(start, end);

  return Promise.resolve({
    records,
    total,
    size: params.pageSize,
    current: params.pageNum,
    pages: Math.ceil(total / params.pageSize),
  });
};

// 模拟获取流程详情
export const mockGetProcessDetail = (id: string): Promise<Process> => {
  const process = mockProcesses.find(item => item.id === id);
  if (!process) {
    return Promise.reject(new Error('流程不存在'));
  }
  return Promise.resolve(process);
};

// 模拟创建流程
export const mockCreateProcess = (data: Partial<Process>): Promise<string> => {
  const newId = String(mockProcesses.length + 1);
  const newProcess: Process = {
    id: newId,
    name: data.name || '新流程',
    description: data.description || '',
    creator: '当前用户',
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    status: 'draft' as ProcessStatus,
  };
  mockProcesses.push(newProcess);
  return Promise.resolve(newId);
};

// 模拟更新流程
export const mockUpdateProcess = (data: Partial<Process>): Promise<boolean> => {
  const index = mockProcesses.findIndex(item => item.id === data.id);
  if (index === -1) {
    return Promise.reject(new Error('流程不存在'));
  }
  mockProcesses[index] = {
    ...mockProcesses[index],
    ...data,
    updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  };
  return Promise.resolve(true);
};

// 模拟删除流程
export const mockDeleteProcess = (ids: string[]): Promise<boolean> => {
  ids.forEach(id => {
    const index = mockProcesses.findIndex(item => item.id === id);
    if (index !== -1) {
      mockProcesses.splice(index, 1);
    }
  });
  return Promise.resolve(true);
};
