import { request } from '@kd/utils';
import type { FlowParams, FlowRecord, RunningInfo } from '@/master/types/flow';

export const saveOrUpdate = (data: FlowParams) =>
  request<FlowParams>({
    url: '/workflow/sysDesWorkflow/saveOrUpdate',
    data,
    method: 'POST',
  });

export const runWorkflow = (workflowId: string) =>
  request<string>({
    url: '/workflow/powerjob/runWorkflow',
    params: { workflowId },
  });

export const getDetailById = (id: string) =>
  request<FlowParams>({
    url: '/workflow/sysDesWorkflow/queryById',
    params: { id },
  });

export const getRunningInfo = (params: {
  wfInstanceId: string;
  appId: string;
}) =>
  request<RunningInfo>({
    url: '/workflow/powerjob/workflowInstanceInfo',
    params,
  });

export const startSingleStepDebug = (params: {
  workflowId: string;
  appId: string;
}) =>
  request<string>({
    url: '/workflow/powerjob/runDebugWorkflow',
    params,
  });

export const nextStepDebug = (data: {
  wfInstanceId: string;
  appId: string;
  params: Record<string, string>;
}) =>
  request({
    url: '/workflow/powerjob/runStepWfInstance',
    data,
    method: 'POST',
  });

export const queryByPage = async (data: {
  pageNum: number;
  pageSize: number;
}) =>
  request<Global.ListResponse<FlowRecord>>({
    url:
      '/workflow/sysDesWorkflow/queryByPage?pageNum=' +
      data.pageNum +
      '&pageSize=' +
      data.pageSize,
    method: 'POST',
    data: {},
  });

export const insert = async (data: any) => {
  return request({
    url: '/workflow/sysDesWorkflow/insert',
    method: 'POST',
    data,
  });
};

export const update = async (data: any) => {
  return request({
    url: '/workflow/sysDesWorkflow/update',
    method: 'POST',
    data,
  });
};

export const delWorkflowById = async (ids: string[]) => {
  return request({
    url: '/workflow/sysDesWorkflow/logicDeleteByIdList?idList=' + ids[0],
    method: 'POST',
    // data,
  });
};

// 工作流实例列表
export const workflowInstanceList = async (data: any) => {
  return request({
    url: '/workflow/powerjob/workflowInstanceList',
    method: 'POST',
    data,
  });
};

// 工作流实例详情
export const workflowInstanceInfoById = async (data: {
  appId: string;
  wfInstanceId: string;
}) => {
  return request({
    url:
      '/workflow/powerjob/workflowInstanceInfo?appId=' +
      data.appId +
      '&wfInstanceId=' +
      data.wfInstanceId,
    method: 'GET',
  });
};

export const retryWfInstance = async (data: {
  appId: string;
  wfInstanceId: string;
}) => {
  return request({
    url:
      '/workflow/powerjob/retryWfInstance?appId=' +
      data.appId +
      '&wfInstanceId=' +
      data.wfInstanceId,
    method: 'GET',
  });
};

export const stopWorkflowInstance = async (id: string) => {
  return request({
    url: '/workflow/powerjob/stopWorkflowInstance?wfInstanceId=' + id,
    method: 'GET',
  });
};
