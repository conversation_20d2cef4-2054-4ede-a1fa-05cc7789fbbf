<template>
  <div class="h-full flex flex-col">
    <!-- Header with process name, back button, step tabs, and actions -->
    <Header
      :process-name="processData.name"
      :last-saved-time="processData.lastSavedTime"
      :current-step="currentStep"
      @update:current-step="currentStep = $event"
      @preview="handlePreview"
      @publish="handlePublish"
    />

    <!-- Main content area that changes based on the selected step -->
    <div class="flex-1 p-4 overflow-y-auto">
      <component :is="currentStepComponent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Header from './components/Header.vue';
import {
  BasicInfo,
  FormDesign,
  DesignCanvas,
  MoreSettings,
} from './components/steps';
import { createProcessContext } from './useProcessContext';

const { processData, publishProcess } = createProcessContext();

// Current step management
const currentStep = ref<number>(0);

// Map step index to component
const currentStepComponent = computed(() => {
  switch (currentStep.value) {
    case 0:
      return BasicInfo;
    case 1:
      return FormDesign;
    case 2:
      return DesignCanvas;
    case 3:
      return MoreSettings;
    default:
      return BasicInfo;
  }
});

// Action handlers
const handlePreview = () => {
  console.log('Preview process');
  // Implement preview functionality
};

const handlePublish = async () => {
  const result = await publishProcess();
  if (result) {
    // Handle successful publish
    console.log('Process published successfully');
  }
};
</script>
