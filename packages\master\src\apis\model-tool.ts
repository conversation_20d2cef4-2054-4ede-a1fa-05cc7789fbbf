import { request } from '@kd/utils';
import { SoftWare } from '../types/software';
import type { ModelData, ModelRecord } from '../types/model';
export const addModel = async (data: Partial<ModelRecord>) => {
  return request({
    url: '/component/sysDesModel/add',
    method: 'POST',
    data,
  });
};

export const deleteModel = async (id: string) => {
  return request({
    url: '/component/sysDesModel/logicDeleteByIdList?idList=' + id,
    method: 'POST',
    data: {},
  });
};

export const updateModel = async (data: Partial<ModelRecord>) => {
  return request({
    url: '/component/sysDesModel/update',
    method: 'POST',
    data,
  });
};

export const requestModelTree = async (id: string) => {
  return request({
    url: '/component/sysDesModel/requestModelTree?sysDesModelId=' + id,
    method: 'POST',
    data: {},
  });
};

// 根据组件id获取组件
export const getComsById = async (params: any) => {
  return request({
    url: '/component/sysDesComponent/queryByIdList',
    method: 'GET',
    params,
  });
};

export const queryByPage = (params: {
  data?: any;
  pageNum: number;
  pageSize: number;
}) =>
  request<Global.ListResponse<ModelRecord>>({
    url:
      '/component/sysDesModel/queryByPage?pageNum=' +
      params.pageNum +
      '&pageSize=' +
      params.pageSize,
    method: 'POST',
    data: params.data,
  });

export const queryById = (data: {
  sysDesModelId: string;
  parentId?: string;
  dataCode?: string;
}) =>
  request<Global.ListResponse<ModelData>>({
    url: '/component/sysDesModelData/queryByPage',
    method: 'POST',
    data,
  });

/**
 * 获取专业软件列表数据
 * @param params
 * @returns
 */
export const getModelSoftwareList = () =>
  request<SoftWare[]>({
    url: '/component/sysDesModel/modelSoftwareList',
    method: 'GET',
  });
