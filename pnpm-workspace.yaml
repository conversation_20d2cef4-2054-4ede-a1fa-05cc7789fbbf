packages:
  - packages/*

catalog:
  '@atlaskit/pragmatic-drag-and-drop': ^1.6.0
  vue: 3.5.12
  vue-hooks-plus: ^2.3.1
  vue-router: ^4.5.1
  pinia: ^3.0.2
  nprogress: ^0.2.0
  dialog-async: ^0.2.0
  core-js: ^3.42.0
  '@kd/hooks': ^1.0.1
  '@kd/utils': 1.1.12
  '@vueuse/core': ^13.1.0
  ant-design-vue: ^4.2.5
  '@iconify/json': ^2.2.158
  '@iconify/vue': ^5.0.0
  dagre: ^0.8.5
  dagre-d3: ^0.6.4
  dayjs: ^1.11.13
  default-passive-events: ^2.0.0
  pinia-plugin-persistedstate: ^4.2.0
  qs: ^6.14.0
  query-string: ^7.1.3
  radash: ^12.1.0
  '@commitlint/cli': ^19.8.0
  '@commitlint/config-conventional': ^19.8.0
  '@eslint/eslintrc': ^3.3.1
  '@eslint/js': ^9.26.0
  '@rollup/plugin-terser': ^0.4.4
  '@types/dagre': ^0.7.52
  '@types/node': ^22.15.14
  '@types/nprogress': ^0.2.3
  '@types/qs': ^6.9.17
  '@typescript-eslint/eslint-plugin': ^8.32.0
  '@typescript-eslint/parser': ^8.32.0
  '@vitejs/plugin-vue': ^5.2.3
  '@vitejs/plugin-vue-jsx': ^4.1.2
  autoprefixer: ^10.4.20
  eslint: ^9.26.0
  eslint-config-prettier: 10.1.2
  eslint-config-standard: 17.0.0-1
  eslint-plugin-html: ^8.1.2
  eslint-plugin-import: ^2.31.0
  eslint-plugin-n: ^17.17.0
  eslint-plugin-prettier: ^5.4.0
  eslint-plugin-promise: ^7.2.1
  eslint-plugin-vue: 7.0.0-beta.4
  fast-glob: ^3.3.2
  globals: ^16.0.0
  husky: ^9.1.6
  less: ^4.3.0
  echarts: ^5.6.0
  lint-staged: ^15.5.2
  postcss: ^8.5.3
  prettier: ^3.5.3
  rollup-plugin-visualizer: ^5.14.0
  sass: ^1.87.0
  stylelint: ^16.19.1
  stylelint-config-standard: ^38.0.0
  tailwindcss: ^3.4.17
  typescript: ^5.8.3
  unplugin-auto-import: ^19.2.0
  unplugin-icons: ^22.1.0
  unplugin-vue-components: ^28.5.0
  unplugin-vue-define-options: ^3.0.0-beta.10
  vite: ^6.3.5
  vite-plugin-compression: ^0.5.1
  vite-plugin-eslint: ^1.8.1
  vite-plugin-html: ^3.2.2
  vite-plugin-lazy-import: ^1.0.7
  vite-plugin-style-import: ^2.0.0
  vite-plugin-top-level-await: ^1.5.0
  vite-plugin-vue-setup-extend: ^0.4.0
  vite-svg-loader: ^5.1.0
  vue-tsc: ^2.2.10
catalogs:
  vxe:
    vxe-pc-ui: ^4.5.36
    vxe-table: ^4.13.18
    xe-utils: ^3.7.2
