<template>
  <div class="approval-node-setting">
    <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <!-- 节点类型 -->
      <a-form-item label="节点类型">
        <a-select v-model:value="formState.type" @change="handleChange">
          <a-select-option value="submit">提交</a-select-option>
          <a-select-option value="approval">审批</a-select-option>
          <a-select-option value="cc">抄送</a-select-option>
          <a-select-option value="end">结束</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 节点名称 -->
      <a-form-item label="节点名称">
        <a-input v-model:value="formState.title" @change="handleChange" />
      </a-form-item>

      <!-- 审批人设置 -->
      <a-form-item v-if="formState.type === 'approval'" label="审批人">
        <a-select
          v-model:value="formState.approverType"
          @change="handleApproverTypeChange"
        >
          <a-select-option value="specific">指定成员</a-select-option>
          <a-select-option value="leader">直属上级</a-select-option>
          <a-select-option value="role">指定角色</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 指定成员 -->
      <a-form-item
        v-if="
          formState.type === 'approval' && formState.approverType === 'specific'
        "
        label="指定成员"
      >
        <a-select
          v-model:value="formState.approvers"
          mode="multiple"
          placeholder="请选择审批人"
          @change="handleChange"
        >
          <a-select-option value="user1">张三</a-select-option>
          <a-select-option value="user2">李四</a-select-option>
          <a-select-option value="user3">王五</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 指定角色 -->
      <a-form-item
        v-if="
          formState.type === 'approval' && formState.approverType === 'role'
        "
        label="指定角色"
      >
        <a-select
          v-model:value="formState.roles"
          mode="multiple"
          placeholder="请选择角色"
          @change="handleChange"
        >
          <a-select-option value="role1">部门经理</a-select-option>
          <a-select-option value="role2">财务主管</a-select-option>
          <a-select-option value="role3">总经理</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 抄送人设置 -->
      <a-form-item v-if="formState.type === 'cc'" label="抄送人">
        <a-select
          v-model:value="formState.ccUsers"
          mode="multiple"
          placeholder="请选择抄送人"
          @change="handleChange"
        >
          <a-select-option value="user1">张三</a-select-option>
          <a-select-option value="user2">李四</a-select-option>
          <a-select-option value="user3">王五</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Node, Graph } from '@antv/x6';

// 组件属性
const props = defineProps<{
  node: Node;
  graph?: Graph;
}>();

// 表单状态
const formState = ref({
  type: 'approval',
  title: '',
  description: '',
  approverType: 'specific',
  approvers: [],
  roles: [],
  ccUsers: [],
});

// 监听节点数据变化
watch(
  () => props.node,
  newNode => {
    if (newNode) {
      const data = newNode.getData() || {};
      formState.value = {
        type: data.type || 'approval',
        title: data.title || '',
        description: data.description || '',
        approverType: data.approverType || 'specific',
        approvers: data.approvers || [],
        roles: data.roles || [],
        ccUsers: data.ccUsers || [],
      };
    }
  },
  { immediate: true }
);

// 处理表单变化
const handleChange = () => {
  // 更新节点描述
  updateDescription();

  // 更新节点数据
  props.node.setData({
    ...props.node.getData(),
    ...formState.value,
  });
};

// 处理审批人类型变化
const handleApproverTypeChange = () => {
  // 根据审批人类型更新描述
  updateDescription();

  // 更新节点数据
  props.node.setData({
    ...props.node.getData(),
    approverType: formState.value.approverType,
  });
};

// 更新节点描述
const updateDescription = () => {
  if (formState.value.type === 'submit') {
    formState.value.description = '提交人：全员可提交';
  } else if (formState.value.type === 'approval') {
    switch (formState.value.approverType) {
      case 'specific':
        formState.value.description = '审批人：指定成员';
        break;
      case 'submitter':
        formState.value.description = '审批人：提交人自选';
        break;
      case 'leader':
        formState.value.description = '审批人：直属上级';
        break;
      case 'role':
        formState.value.description = '审批人：指定角色';
        break;
      default:
        formState.value.description = '审批人：请设置';
    }
  } else if (formState.value.type === 'cc') {
    formState.value.description = '抄送人：请设置';
  } else if (formState.value.type === 'end') {
    formState.value.description = '流程结束';
  }
};
</script>

<style scoped>
.approval-node-setting {
  height: 100%;
}
</style>
