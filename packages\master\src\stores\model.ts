import { getModelSoftwareList, queryByPage } from '../apis/model-tool';
import { ModelRecord } from '../types/model';
import { SoftWare } from '../types/software';
import { defineStore } from 'pinia';

const modelStore = defineStore('model', {
  state: () => ({
    software: [] as SoftWare[],
    models: [] as ModelRecord[],
  }),
  actions: {
    async initSoftware() {
      const res = await getModelSoftwareList();
      this.software = res || [];
    },
    getSoftwareById(id: string) {
      return this.software.find(item => item.desProfessionalSoftwareId === id);
    },
    async initModels(params: {
      data: {
        desProfessionalSoftwareId: string;
      };
      pageNum: number;
      pageSize: number;
    }) {
      const res = await queryByPage(params);
      this.models = res.records || [];
    },
    getModelById(id: string) {
      return this.models.find(item => item.sysDesModelId === id);
    },
  },
  persist: {
    pick: ['software'],
    storage: localStorage,
  },
});

export default modelStore;
