/**
 * @file edgeUtils.ts
 * @description Utility functions for edge handling
 */

import { Graph } from '@antv/x6';
import { EdgeConnectedEvent, GlobalDataMap, NodeData, Ref } from './types';

/**
 * Validates if a connection between two ports is valid
 * @param sourcePort - The source port
 * @param targetPort - The target port
 * @returns Whether the connection is valid
 */
export function isValidConnection(sourcePort: any, targetPort: any): boolean {
  // Check if ports are from the same component
  const isSameNodeId =
    sourcePort.customData.nodeId === targetPort.customData.nodeId;

  // Check if the connection is valid based on port types
  const isValidPortTypes =
    (sourcePort.id.includes('global') && targetPort.customData.type === 'in') ||
    (sourcePort.customData.type === 'out' &&
      targetPort.customData.type === 'in');

  return !isSameNodeId && isValidPortTypes;
}

/**
 * Creates a global data mapping from a connection event
 * @param event - The edge connected event
 * @returns The global data mapping
 */
export function createGlobalDataMapping(
  event: EdgeConnectedEvent
): GlobalDataMap {
  const { edge } = event;
  const sourcePortId = edge.getSource().port;
  const targetPortId = edge.getTarget().port;

  const sourceNode = edge.getSourceNode();
  const targetNode = edge.getTargetNode();

  const sourcePort = sourceNode.getPort(sourcePortId);
  const targetPort = targetNode.getPort(targetPortId);

  return {
    fromNode: 'global',
    toNode: targetPort.customData.nodeId,
    fromParam: sourcePort.customData.id,
    toParam: targetPort.customData.id,
    calculate: '',
    flowForward: targetPort.customData.type,
    id: edge.id,
  };
}

/**
 * Creates a node data mapping from a connection event
 * @param event - The edge connected event
 * @returns The node data mapping
 */
export function createNodeDataMapping(event: EdgeConnectedEvent): NodeData {
  const { edge } = event;
  const sourcePortId = edge.getSource().port;
  const targetPortId = edge.getTarget().port;

  const sourceNode = edge.getSourceNode();
  const targetNode = edge.getTargetNode();

  const sourcePort = sourceNode.getPort(sourcePortId);
  const targetPort = targetNode.getPort(targetPortId);

  return {
    fromNode: sourcePort.customData.nodeId,
    toNode: targetPort.customData.nodeId,
    fromParam: sourcePort.customData.id,
    toParam: targetPort.customData.id,
    fromName: sourcePort.customData.name,
    toName: targetPort.customData.name,
    calculate: '',
    flowForward: targetPort.customData.type,
    id: edge.id,
  };
}

/**
 * Handles edge connection events
 * @param event - The edge connected event
 * @param graph - The graph instance
 * @param nodeData - The node data array
 * @param globalDataMap - The global data map array
 */
export function handleEdgeConnected(
  event: EdgeConnectedEvent,
  graph: Graph,
  nodeData: Ref<NodeData[]>,
  globalDataMap: Ref<GlobalDataMap[]>
): void {
  const { edge } = event;
  const sourcePortId = edge.getSource().port;
  const targetPortId = edge.getTarget().port;

  const sourceNode = edge.getSourceNode();
  const targetNode = edge.getTargetNode();

  const sourcePort = sourceNode.getPort(sourcePortId);
  const targetPort = targetNode.getPort(targetPortId);

  // Validate the connection
  if (!isValidConnection(sourcePort, targetPort)) {
    graph.removeEdge(edge);
    return;
  }

  // Handle global parameter connections
  if (sourcePort.id.includes('global')) {
    globalDataMap.value.push(createGlobalDataMapping(event));
  } else {
    // Handle regular node connections
    nodeData.value.push(createNodeDataMapping(event));
  }
}
