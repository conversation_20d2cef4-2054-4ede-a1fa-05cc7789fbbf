/**
 * @file ApprovalNode.tsx
 * @description 审批节点组件，用于在审批流程图中显示审批节点
 */

import { Graph, Node } from '@antv/x6';
import RightOutlined from '~icons/ant-design/right-outlined';
import style from '../styles/approvalNode.module.css';

/**
 * 审批节点组件
 */
export default defineComponent({
  name: 'ApprovalNode',
  props: {
    /** 节点实例 */
    node: {
      type: Object as PropType<Node>,
      required: true,
    },
    /** 图形实例 */
    graph: Object as PropType<Graph>,
  },
  setup(props) {
    // 节点数据
    const data = ref(props.node?.getData() || {});

    // 监听节点数据变化
    props.node!.on('change:data', ({ current }) => {
      data.value = current;
    });

    // 获取节点类型对应的颜色
    const getNodeColor = computed(() => {
      switch (data.value.type) {
        case 'submit':
          return '#5362f2'; // 提交节点颜色 - 蓝绿色
        case 'approval':
          return '#1677ff'; // 审批节点颜色 - 橙色
        case 'cc':
          return '#40CFA0'; // 抄送节点颜色 - 蓝色
        case 'end':
          return '#CCCCCC'; // 结束节点颜色 - 灰色
        default:
          return '#40CFA0'; // 默认颜色
      }
    });

    return { data, getNodeColor };
  },

  render() {
    return (
      <div
        class={[style.approvalNode, this.data.isSelected ? style.selected : '']}
        style={{ borderColor: this.getNodeColor }}
      >
        <div
          class={style.nodeHeader}
          style={{
            backgroundColor:
              this.data.type === 'condition' ? '#f0f0f0' : this.getNodeColor,
          }}
        >
          <span
            class={style.nodeTitle}
            style={{ color: this.data.type === 'condition' ? '#333' : '#fff' }}
          >
            {this.data.title}
          </span>
        </div>
        <div class={style.nodeBody}>
          <div class={style.nodeDescription}>{this.data.description}</div>
          <div class={style.nodeAction}>
            <RightOutlined />
          </div>
        </div>
      </div>
    );
  },
});
