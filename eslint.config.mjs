import vue from 'eslint-plugin-vue';
import typescriptEslint from '@typescript-eslint/eslint-plugin';
import html from 'eslint-plugin-html';
import globals from 'globals';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default [
  ...compat.extends(
    'plugin:vue/vue3-recommended',
    'standard',
    'plugin:prettier/recommended',
    './.eslintrc-auto-import.json'
  ),
  {
    plugins: {
      vue,
      '@typescript-eslint': typescriptEslint,
      html,
    },

    languageOptions: {
      globals: {
        ...globals.browser,
        defineOptions: 'writable',
      },

      ecmaVersion: 'latest',
      sourceType: 'module',

      parserOptions: {
        parser: '@typescript-eslint/parser',
      },
    },

    rules: {
      'no-unused-vars': 'off',
      'vue/multi-word-component-names': 'off',
      'vue/no-multiple-template-root': 'off',
      'no-undef': 'off',
      indent: 'off',
      'vue/comment-directive': 'off',
      'vue/valid-v-for': 'off',
      'vue/valid-v-slot': 'off',
    },
    ignores: [
      'packages/**/src/lib/**',
      'packages/**/src/auto-imports.d.ts',
      'packages/**/src/components.d.ts',
    ],
  },
];
