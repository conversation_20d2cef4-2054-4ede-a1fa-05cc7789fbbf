<template>
  <div class="condition-node-setting">
    <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <!-- 节点名称 -->
      <a-form-item label="节点名称">
        <a-input v-model:value="formState.title" @change="handleChange" />
      </a-form-item>
      
      <!-- 条件列表 -->
      <div class="mb-4">
        <div class="flex justify-between items-center mb-2">
          <h4 class="text-base font-medium">条件列表</h4>
          <a-button type="link" @click="handleAddCondition">
            <template #icon><PlusOutlined /></template>
            添加条件
          </a-button>
        </div>
        
        <!-- 条件项 -->
        <div v-for="(item, index) in formState.conditions" :key="item.id" class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <h5 class="text-sm font-medium">条件 {{ index + 1 }}</h5>
            <a-button 
              v-if="formState.conditions.length > 2"
              type="link" 
              danger 
              @click="handleDeleteCondition(item.id)"
            >
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </div>
          
          <!-- 条件表达式 -->
          <a-input
            v-model:value="item.condition"
            placeholder="请设置条件"
            @change="handleChange"
          />
          
          <!-- 条件组 -->
          <div class="mt-2 pl-4 border-l-2 border-gray-200">
            <div v-for="(group, groupIndex) in item.groups || []" :key="groupIndex" class="mb-2">
              <div class="flex items-center gap-2">
                <a-select v-model:value="group.field" style="width: 120px" @change="handleChange">
                  <a-select-option value="amount">金额</a-select-option>
                  <a-select-option value="department">部门</a-select-option>
                  <a-select-option value="role">角色</a-select-option>
                </a-select>
                
                <a-select v-model:value="group.operator" style="width: 80px" @change="handleChange">
                  <a-select-option value="eq">等于</a-select-option>
                  <a-select-option value="gt">大于</a-select-option>
                  <a-select-option value="lt">小于</a-select-option>
                  <a-select-option value="in">属于</a-select-option>
                </a-select>
                
                <a-input 
                  v-model:value="group.value" 
                  style="width: 120px" 
                  placeholder="值"
                  @change="handleChange"
                />
                
                <a-button 
                  v-if="item.groups.length > 1"
                  type="link" 
                  danger 
                  size="small"
                  @click="handleDeleteGroup(item.id, groupIndex)"
                >
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>
            
            <!-- 添加条件组 -->
            <a-button type="link" size="small" @click="handleAddGroup(item.id)">
              <template #icon><PlusOutlined /></template>
              添加条件组
            </a-button>
          </div>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Node, Graph } from '@antv/x6';
import { generateUUID } from 'shared/utils';
import PlusOutlined from '~icons/ant-design/plus-outlined';
import DeleteOutlined from '~icons/ant-design/delete-outlined';

// 组件属性
const props = defineProps<{
  node: Node;
  graph?: Graph;
}>();

// 表单状态
const formState = ref({
  title: '条件分支',
  conditions: [] as any[],
});

// 监听节点数据变化
watch(
  () => props.node,
  (newNode) => {
    if (newNode) {
      const data = newNode.getData() || {};
      
      // 初始化条件组
      const conditions = (data.conditions || []).map((condition: any) => {
        return {
          ...condition,
          groups: condition.groups || [
            {
              field: 'amount',
              operator: 'gt',
              value: '',
            },
          ],
        };
      });
      
      formState.value = {
        title: data.title || '条件分支',
        conditions: conditions.length > 0 ? conditions : [
          {
            id: generateUUID(),
            condition: '请设置条件',
            groups: [
              {
                field: 'amount',
                operator: 'gt',
                value: '',
              },
            ],
          },
          {
            id: generateUUID(),
            condition: '其他情况',
            groups: [],
          },
        ],
      };
    }
  },
  { immediate: true }
);

// 处理表单变化
const handleChange = () => {
  // 更新节点数据
  props.node.setData({
    ...props.node.getData(),
    title: formState.value.title,
    conditions: formState.value.conditions,
  });
  
  // 更新端口
  updatePorts();
};

// 添加条件
const handleAddCondition = () => {
  formState.value.conditions.push({
    id: generateUUID(),
    condition: '请设置条件',
    groups: [
      {
        field: 'amount',
        operator: 'gt',
        value: '',
      },
    ],
  });
  
  handleChange();
};

// 删除条件
const handleDeleteCondition = (id: string) => {
  // 至少保留两个条件
  if (formState.value.conditions.length <= 2) return;
  
  formState.value.conditions = formState.value.conditions.filter(
    item => item.id !== id
  );
  
  handleChange();
};

// 添加条件组
const handleAddGroup = (conditionId: string) => {
  const condition = formState.value.conditions.find(
    item => item.id === conditionId
  );
  
  if (condition) {
    condition.groups = condition.groups || [];
    condition.groups.push({
      field: 'amount',
      operator: 'gt',
      value: '',
    });
    
    handleChange();
  }
};

// 删除条件组
const handleDeleteGroup = (conditionId: string, groupIndex: number) => {
  const condition = formState.value.conditions.find(
    item => item.id === conditionId
  );
  
  if (condition && condition.groups && condition.groups.length > 1) {
    condition.groups.splice(groupIndex, 1);
    handleChange();
  }
};

// 更新端口
const updatePorts = () => {
  // 获取当前端口
  const currentPorts = props.node.getPorts() || [];
  const currentPortIds = currentPorts.map(port => port.id);
  
  // 需要的端口数量
  const neededPorts = formState.value.conditions.length;
  
  // 移除多余的端口
  for (let i = neededPorts; i < currentPortIds.length; i++) {
    if (currentPortIds[i] && !['top', 'left', 'right'].includes(currentPortIds[i])) {
      props.node.removePort(currentPortIds[i]);
    }
  }
  
  // 添加缺少的端口
  for (let i = 0; i < neededPorts; i++) {
    const portId = `port-${i}`;
    if (!currentPortIds.includes(portId)) {
      props.node.addPort({
        id: portId,
        group: 'bottom',
      });
    }
  }
};
</script>

<style scoped>
.condition-node-setting {
  height: 100%;
}
</style>
