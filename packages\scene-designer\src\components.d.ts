/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AntDesignAppstoreOutlined: typeof import('~icons/ant-design/appstore-outlined')['default']
    AntDesignCloseOutlined: typeof import('~icons/ant-design/close-outlined')['default']
    AntDesignEyeOutlined: typeof import('~icons/ant-design/eye-outlined')['default']
    AntDesignMenuOutlined: typeof import('~icons/ant-design/menu-outlined')['default']
    AntDesignSaveOutlined: typeof import('~icons/ant-design/save-outlined')['default']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATypography: typeof import('ant-design-vue/es')['Typography']
    ATypographyTitle: typeof import('ant-design-vue/es')['TypographyTitle']
    ColorPicker: typeof import('./components/ColorPicker/index.tsx')['default']
    ConfigModal: typeof import('./components/KRadio/configModal.tsx')['default']
    copy: typeof import('./components/KRadio copy/index.tsx')['default']
    HugeiconsPropertyEdit: typeof import('~icons/hugeicons/property-edit')['default']
    ImageSelect: typeof import('./components/ImageSelect/index.tsx')['default']
    KBinder: typeof import('./components/KBinder/index.tsx')['default']
    KButton: typeof import('./components/KButton/index.tsx')['default']
    KCard: typeof import('./components/KCard/index.tsx')['default']
    KCheckbox: typeof import('./components/KCheckbox/index.tsx')['default']
    KCheckBox: typeof import('./components/KCheckBox/index.tsx')['default']
    KContainer: typeof import('./components/KContainer/index.tsx')['default']
    KDatePicker: typeof import('./components/KDatePicker/index.tsx')['default']
    KEmpty: typeof import('./components/KEmpty/index.tsx')['default']
    KGrid: typeof import('./components/KGrid/index.tsx')['default']
    KImage: typeof import('./components/KImage/index.tsx')['default']
    KInput: typeof import('./components/KInput/index.tsx')['default']
    KRadio: typeof import('./components/KRadio/index.tsx')['default']
    KRangePicker: typeof import('./components/KRangePicker/index.tsx')['default']
    KSelect: typeof import('./components/KSelect/index.tsx')['default']
    KSwitch: typeof import('./components/KSwitch/index.tsx')['default']
    KText: typeof import('./components/KText/index.tsx')['default']
    MaterialSymbolsDeleteOutline: typeof import('~icons/material-symbols/delete-outline')['default']
    OptionsConfigModal: typeof import('./components/OptionsConfigModal/index.tsx')['default']
    OptionsModal: typeof import('./components/OptionsConfigModal/index.jsx')['default']
    RenderCheckbox: typeof import('./components/KCheckbox/RenderCheckbox.tsx')['default']
    RenderIem: typeof import('./components/KRadio/RenderRadio.jsx')['default']
    RenderRadio: typeof import('./components/KRadio/RenderRadio.tsx')['default']
    RenderSelect: typeof import('./components/KSelect/RenderSelect.tsx')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectModal: typeof import('./components/SelectModal/index.tsx')['default']
    UnitField: typeof import('./components/UnitField/index.tsx')['default']
    Uploader: typeof import('./components/Uploader/index.tsx')['default']
  }
}
