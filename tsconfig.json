{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "es2020", "moduleResolution": "bundler", "strict": true, "jsx": "preserve", "sourceMap": true, "jsxImportSource": "vue", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "allowJs": false, "importHelpers": true, "noUnusedLocals": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}}