const schemaMap = new Map<string, Widget.RenderConfig>();

const schemaStore = defineStore('schema', {
  state: () => ({
    jsonSchema: [] as Widget.RenderConfig[],
    schemaMap,
    currentId: '',
  }),
  actions: {
    initSchema(json: Widget.RenderConfig[]) {
      this.jsonSchema = json;
      json.forEach(comp => {
        this.initSchemaMap(comp);
      });
    },
    initSchemaMap(item: Widget.RenderConfig) {
      this.schemaMap.set(item.id, item);
      if (item.children?.length) {
        item.children.forEach(t => {
          this.initSchemaMap(t);
        });
      }
    },
    getSchemaById(id: string) {
      return this.schemaMap.get(id) as Widget.RenderConfig | null;
    },

    addSchema(json: Widget.RenderConfig, parentId?: string) {
      if (!parentId) {
        this.schemaMap.set(json.id, json);
        this.jsonSchema.push(json);
      } else {
        const parentSchema = this.getSchemaById(parentId);
        if (parentSchema) {
          if (!parentSchema.children) parentSchema.children = [];
          json.parentId = parentId;
          parentSchema.children.push(json);
          this.schemaMap.set(json.id, json);
        }
      }
    },
    setSchema(id: string, json: Widget.RenderConfig) {
      const schema = this.getSchemaById(id);
      schema && Object.assign(schema, json);
    },
    deleteSchema(id: string, parentId?: string) {
      if (parentId) {
        const parentSchema = this.getSchemaById(parentId);
        if (parentSchema && parentSchema?.children) {
          parentSchema.children = parentSchema.children.filter(
            t => t.id !== id
          );
          this.schemaMap.delete(id);
        }
      } else {
        this.jsonSchema = this.jsonSchema.filter(t => t.id !== id);
        this.schemaMap.delete(id);
      }
      this.currentId = '';
    },
    setCurrentId(id: string) {
      this.currentId = id;
    },
  },
  getters: {
    currentSchema: state => state.schemaMap.get(state.currentId),
  },
});

export default schemaStore;
