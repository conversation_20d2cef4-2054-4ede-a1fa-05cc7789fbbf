/**
 * @file events.ts
 * @description 工作流图形事件处理模块
 */

import eventEmitter from 'shared/utils/eventEmitter';
import { WorkflowGraph } from './WorkflowGraph';
import { Node } from '@antv/x6';
/**
 * 初始化图形事件
 * @param graph - 工作流图形实例
 */
export function initEvents(graph: WorkflowGraph): void {
  const setCurrentNode = (node?: Node) => {
    graph.currentNode = node;
    const allNodes = graph.getNodes();
    allNodes.forEach(t => {
      t.setData({
        ...t.getData(),
        isSelected: node ? t.id === node.id : false,
      });
    });
    eventEmitter.emit('node:select', node);
  };
  // 绑定撤销快捷键
  graph.bindKey(['meta+z', 'ctrl+z'], () => {
    if (graph.canUndo()) {
      graph.undo();
    }
    return false;
  });

  // 绑定删除快捷键
  graph.bindKey('backspace', () => {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.removeCells(cells);
    }
  });

  // 绑定重做快捷键
  graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
    if (graph.canRedo()) {
      graph.redo();
    }
    return false;
  });

  // 鼠标进入元素事件
  graph.on('cell:mouseenter', ({ cell }) => {
    if (cell.isNode()) {
      // 显示节点的所有连接桩
      const ports = cell.getPorts();
      ports.forEach(port => {
        cell.setPortProp(port.id!, 'attrs/circle', {
          style: 'visibility: visible',
        });
      });
      const nodeSize = cell.getSize();
      cell.addTools([
        {
          name: 'button-remove',
          args: {
            x: nodeSize.width - 4,
            y: 6,
          },
        },
      ]);
    } else if (cell.isEdge()) {
      cell.addTools([
        {
          name: 'button-remove',
          args: {
            distance: '50%',
          },
        },
      ]);
      // 为边添加箭头工具
      cell.addTools([
        {
          name: 'target-arrowhead',
          args: {
            attrs: {
              fill: '#757de8',
            },
          },
        },
      ]);
    }
  });

  // 鼠标离开元素事件
  graph.on('cell:mouseleave', ({ cell }) => {
    // 移除工具
    cell.removeTools();
    if (cell.isNode()) {
      // 隐藏节点的所有连接桩
      const ports = cell.getPorts();
      ports.forEach(port => {
        cell.setPortProp(port.id!, 'attrs/circle', {
          style: 'visibility: hidden',
        });
      });
    }
  });

  // 节点点击事件
  graph.on('node:click', ({ node }) => {
    setCurrentNode(node);
  });

  // 节点添加事件
  graph.on('node:added', ({ node }) => {
    graph.getNodesParams();
    setCurrentNode(node);
  });

  // 节点数据变化事件
  graph.on('node:change:data', ({ node }) => {
    graph.getNodesParams();
  });

  // 节点移除事件
  graph.on('node:removed', () => {
    graph.getNodesParams();
  });

  // 节点选中事件
  graph.on('node:selected', ({ node }) => {
    // 移除工具并隐藏连接桩
    node.removeTools();
    const ports = node.getPorts();
    ports.forEach(port => {
      node.setPortProp(port.id!, 'attrs/circle', {
        style: 'visibility: hidden',
      });
    });
  });

  // 添加画布点击事件，取消节点选择
  graph.on('blank:click', () => {
    setCurrentNode();
  });

  // 监听节点嵌入事件，记录 Ctrl 键状态
  graph.on('node:embedding', ({ e }: { e: any }) => {
    graph.ctrlPressed = e.metaKey || e.ctrlKey;
  });

  // 监听节点嵌入完成事件，重置 Ctrl 键状态
  graph.on('node:embedded', () => {
    graph.ctrlPressed = false;
  });

  // 监听节点大小变化事件
  graph.on('node:change:size', ({ node, options }) => {
    if (options && options.skipParentHandler) {
      return;
    }

    const children = node.getChildren();
    if (children && children.length) {
      node.prop('originSize', node.getSize());
    }
  });

  // 监听节点位置变化事件
  graph.on('node:change:position', ({ node, options }) => {
    // 如果设置了跳过父节点处理或者按下了 Ctrl 键，则跳过
    if (options.skipParentHandler || graph.ctrlPressed) {
      return;
    }

    // 如果节点有子节点，记录原始位置
    const children = node.getChildren();
    if (children && children.length) {
      node.prop('originPosition', node.getPosition());
    }

    // 如果节点有父节点，调整父节点大小
    const parent = node.getParent();
    if (parent && parent.isNode()) {
      // 获取父节点的原始大小，如果没有则记录当前大小
      let originSize = parent.prop('originSize');
      if (originSize == null) {
        originSize = parent.getSize();
        parent.prop('originSize', originSize);
      }

      // 获取父节点的原始位置，如果没有则记录当前位置
      let originPosition = parent.prop('originPosition');
      if (originPosition == null) {
        originPosition = parent.getPosition();
        parent.prop('originPosition', originPosition);
      }

      // 初始化父节点的边界坐标
      let x = originPosition.x;
      let y = originPosition.y;
      let cornerX = originPosition.x + originSize.width;
      let cornerY = originPosition.y + originSize.height;
      let hasChange = false;

      // 遍历所有子节点，计算新的边界
      const children = parent.getChildren();
      if (children) {
        children.forEach(child => {
          // 获取子节点的边界框，并添加内边距
          const bbox = child.getBBox().inflate(graph.embedPadding);
          const corner = bbox.getCorner();

          // 更新父节点的边界
          if (bbox.x < x) {
            x = bbox.x;
            hasChange = true;
          }

          if (bbox.y < y) {
            y = bbox.y;
            hasChange = true;
          }

          if (corner.x > cornerX) {
            cornerX = corner.x;
            hasChange = true;
          }

          if (corner.y > cornerY) {
            cornerY = corner.y;
            hasChange = true;
          }
        });
      }

      // 如果边界有变化，更新父节点的位置和大小
      if (hasChange) {
        parent.prop(
          {
            position: { x, y },
            size: { width: cornerX - x, height: cornerY - y },
          },
          { skipParentHandler: true } // 设置跳过父节点处理标记，避免循环
        );
      }
    }
  });
}
