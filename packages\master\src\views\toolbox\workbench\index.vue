<template>
  <div class="flex flex-col h-full">
    <div class="px-[20%] py-1">
      <Step :current />
    </div>
    <div class="flex-1 px-4 overflow-hidden">
      <KeepAlive>
        <component :is="currentComponent" ref="componentRef" />
      </KeepAlive>
    </div>
    <div class="py-1 text-center">
      <a-space>
        <a-button v-if="current != 0" @click="setStep('prev')">上一步</a-button>
        <a-button v-if="!isLast" @click="setStep('next')"> 下一步 </a-button>
        <div v-show="current === 2" id="footer"></div>
        <a-button type="primary" ghost @click="handleSave"> 临时保存 </a-button>
        <a-button v-if="isLast" type="primary" @click="handleSave">
          保存
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { message } from 'ant-design-vue';
import Step from './components/step/index.vue';
import { createTool, queryToolById, updateTool } from '@/master/apis/tools';
import useSteps from './useSteps';
import { createToolContext } from './useTool';
import { isObjectString } from 'shared/utils';

const router = useRouter();
const route = useRoute();
const componentRef = useTemplateRef<any>('componentRef');
const professionalSoftwareId = route.query.professionalSoftwareId as string;
const { current, currentComponent, isLast, next, prev } = useSteps();
const { toolParams } = createToolContext();
// 设置步骤
const setStep = async (type: string) => {
  if (type === 'next') {
    const validateFunc = componentRef.value?.validate;
    if (validateFunc && typeof validateFunc === 'function') {
      const isValid = await validateFunc();
      if (!isValid) {
        return;
      }
    }
    next();
  } else if (type === 'prev') {
    prev();
  }
};

const handleSave = async () => {
  await componentRef.value?.validate();
  const {
    commands,
    inputParams,
    outputParams,
    pageStructure,
    paramsMap,
    componentIcon,
  } = toolParams.value;
  console.log('item', inputParams);
  console.log('paramsMap', paramsMap);
  const finalInputParams = inputParams.map(t => {
    const item = paramsMap.find(item => item.command === t.commandCode);
    return {
      ...t,
      name: item?.paramCode,
    };
  });
  console.log('item', finalInputParams);
  debugger;
  const params = {
    ...toolParams.value,
    professionalSoftwareId,
    componentOrder: 0,
    pageStructure: JSON.stringify(pageStructure),
    desProfessionalSoftwareId: professionalSoftwareId,
    commands: JSON.stringify(commands),
    inputParams: JSON.stringify(finalInputParams),
    outputParams: JSON.stringify(outputParams),
    paramsMap: JSON.stringify(paramsMap),
    componentIcon: JSON.stringify(componentIcon),
  };
  const action = route.query.sysDesComponentId ? updateTool : createTool;
  const res = await action(params);
  if (res.code === 200) {
    message.success('保存成功');
    router.push('/toolbox');
  } else {
    message.error('保存失败，请稍后再试');
  }
};

onMounted(async () => {
  const sysDesComponentId = route.query.sysDesComponentId as string;
  toolParams.value.desProfessionalSoftwareId = professionalSoftwareId;
  if (sysDesComponentId) {
    const res = await queryToolById(sysDesComponentId);
    toolParams.value = {
      ...res,
      commands: JSON.parse(res.commands),
      inputParams: JSON.parse(res.inputParams),
      outputParams: JSON.parse(res.outputParams),
      pageStructure: JSON.parse(res.pageStructure),
      paramsMap: JSON.parse(res.paramsMap),
      componentIcon: isObjectString(res.componentIcon)
        ? JSON.parse(res.componentIcon)
        : res.componentIcon,
    };
  }
});
</script>
