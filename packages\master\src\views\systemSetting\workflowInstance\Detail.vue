<template>
  <div class="h-full pt-4">
    <div class="w-full h-full bg-[#ffffff] p-2 flex flex-col rounded-lg">
      <div class="flex justify-between">
        <a-button type="primary" @click="handleBack">返回</a-button>
        <div class="flex gap-2">
          <a-button @click="getDetail">刷新</a-button>
          <a-button
            type="primary"
            style="background-color: #e6a33d"
            @click="handleRetry"
            >重试</a-button
          >
          <a-button type="primary" danger @click="handleStop">停止</a-button>
        </div>
      </div>
      <div class="flex flex-col gap-4 mt-4 p-4">
        <div>
          状态： <span>{{ statusText }}</span>
          <!-- 状态： <span>{{ info.status }}</span> -->
        </div>
        <div class="grid grid-cols-3">
          <div>
            工作流ID：<span>{{ info.workflowId }}</span>
          </div>
          <div>
            工作流实例ID：<span>{{ info.wfInstanceId }}</span>
          </div>
        </div>
        <div class="grid grid-cols-3">
          <div>
            预计执行时间：<span>{{ info.workflowId }}</span>
          </div>
          <div>
            触发时间：<span>{{ info.expectedTriggerTime }}</span>
          </div>
          <div>
            结束时间：<span>{{ info.finishedTime }}</span>
          </div>
        </div>
        <div>
          启动参数：<span>{{ info.wfInitParams }}</span>
        </div>
        <div>
          上下文：<span>{{ info.wfContext }}</span>
        </div>
        <div class="flex flex-col">
          任务结果（tips：点击节点可查看任务实例）：<span>{{
            info.result
          }}</span>
        </div>
      </div>
      <div class="wf-content">
        <div class="wf-content-header">
          <a-tooltip title="">
            <IconParkOutlineFileSuccess class="cursor-pointer" />
          </a-tooltip>
          <a-tooltip title="">
            <SystemUiconsRefreshAlt class="cursor-pointer" />
          </a-tooltip>
          <a-tooltip title="">
            <MingcuteZoomOutLine class="cursor-pointer" />
          </a-tooltip>
          <a-tooltip title="">
            <IconamoonZoomIn class="cursor-pointer" />
          </a-tooltip>
          <a-tooltip title="">
            <EpAim class="cursor-pointer" />
          </a-tooltip>
          <a-tooltip title="">
            <OcticonScreenFull24 class="cursor-pointer" />
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  retryWfInstance,
  stopWorkflowInstance,
  workflowInstanceInfoById,
} from '@/master/apis/flow';
import { useRouter, useRoute } from 'vue-router';
import IconParkOutlineFileSuccess from '~icons/icon-park-outline/file-success';
import SystemUiconsRefreshAlt from '~icons/system-uicons/refresh-alt';
import MingcuteZoomOutLine from '~icons/mingcute/zoom-out-line';
import IconamoonZoomIn from '~icons/iconamoon/zoom-in';
import EpAim from '~icons/ep/aim';
import OcticonScreenFull24 from '~icons/octicon/screen-full-24';
import { statusList, statusDetailList } from './helper';
import { message } from 'ant-design-vue';

const router = useRouter();
const route = useRoute();
const id = route.query.id as string;
const info = ref<any>({});
const loading = ref<boolean>(false);
const statusText = ref<string>('');

const params = ref({
  appId: '16',
  wfInstanceId: id,
});

const handleBack = () => {
  router.back();
};

const getDetail = async () => {
  loading.value = true;
  const res = await workflowInstanceInfoById(params.value);
  if (res) {
    info.value = res;
    statusText.value = statusList.find(s => s.key === res.status)
      ?.label as string;
  }

  loading.value = false;
};

const handleRetry = async () => {
  try {
    await retryWfInstance(params.value);
  } catch (error) {
    console.log('error :>>', error);
    // message.error(error);
  }
};

const handleStop = async () => {
  try {
    await stopWorkflowInstance(id);
  } catch (error: any) {
    console.log('error :>>', error);
    message.error(error);
  }
};

onMounted(() => {
  getDetail();
});
</script>
<style scoped lang="less">
.ant-spin-nested-loading .spin-content {
  height: 100%;
}

span {
  font-weight: bold;
}

.wf-content {
  flex: 1;
  border: 1px solid #ececec;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #fff;
}

.wf-content-header {
  border-bottom: 1px solid #ececec;
  height: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-left: 10px;
}
</style>
