import { request } from '@kd/utils';

/**
 * 获取列表数据
 * @param params
 * @returns
 */
export const getCommandsList = async (params: any) => {
  return request({
    url:
      '/component/sysDesCommand/getList?pageNum=' +
      params.pageNum +
      '&pageSize=' +
      params.pageSize,
    method: 'POST',
    data: params.data,
  });
};

/**
 * 删除
 * @param params
 * @returns
 */
export const DelCommandById = async (id: string) => {
  return request({
    url: '/component/sysDesCommand/deleteSysDesCommand?sysDesCommandId=' + id,
    method: 'GET',
  });
};

/**
 * 新增
 * @param params
 * @returns
 */
export const addCommand = async (data: any) => {
  return request({
    url: '/component/sysDesCommand/addSysDesCommand',
    method: 'POST',
    data,
  });
};

/**
 * 修改
 * @param params
 * @returns
 */
export const updateCommand = async (data: any) => {
  return request({
    url: '/component/sysDesCommand/updateSysDesCommand',
    method: 'POST',
    data,
  });
};
