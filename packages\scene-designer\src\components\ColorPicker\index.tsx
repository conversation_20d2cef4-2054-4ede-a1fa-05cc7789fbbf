import { ColorPicker } from 'vue3-colorpicker';
import 'vue3-colorpicker/style.css';

export default defineComponent({
  name: 'ColorPicker',
  props: {
    value: {
      type: String as PropType<string>,
      default: '#fff',
    },
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    const pureColor = ref(props.value);

    watch(pureColor, value => {
      emit('update:value', value);
      emit('change', value);
    });
    return () => <ColorPicker v-model:pureColor={pureColor.value} />;
  },
});
