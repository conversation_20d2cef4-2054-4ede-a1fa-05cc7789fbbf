import * as echarts from 'echarts';
import type { XAXisComponentOption, YAXisComponentOption } from 'echarts';
import type {
  ECElementEvent,
  SelectChangedPayload,
  HighlightPayload,
} from 'echarts/types/src/util/types.d';
import type {
  TitleComponentOption,
  TooltipComponentOption,
  GridComponentOption,
  LegendComponentOption,
  ToolboxComponentOption,
  VisualMapComponentOption,
  DatasetComponentOption,
  GraphicComponentOption,
  BrushComponentOption,
} from 'echarts/components';
import type {
  BarSeriesOption,
  LineSeriesOption,
  PieSeriesOption,
  ScatterSeriesOption,
  HeatmapSeriesOption,
  GaugeSeriesOption,
  PictorialBarSeriesOption,
} from 'echarts/charts';

type BaseOptionType =
  | XAXisComponentOption
  | YAXisComponentOption
  | TitleComponentOption
  | TooltipComponentOption
  | LegendComponentOption
  | GridComponentOption
  | ToolboxComponentOption
  | VisualMapComponentOption
  | DatasetComponentOption
  | GraphicComponentOption
  | BrushComponentOption;
type BaseOption = echarts.ComposeOption<BaseOptionType>;
type LineECOption = echarts.ComposeOption<LineSeriesOption | BaseOptionType>;
type BarECOption = echarts.ComposeOption<BarSeriesOption | BaseOptionType>;
type PieECOption = echarts.ComposeOption<PieSeriesOption | BaseOptionType>;
type HeatmapECOption = echarts.ComposeOption<
  HeatmapSeriesOption | BaseOptionType
>;
type ScatterECOption = echarts.ComposeOption<
  ScatterSeriesOption | BaseOptionType
>;
type PictorialBarECOption = echarts.ComposeOption<
  PictorialBarSeriesOption | BaseOptionType
>;
type GuageECOption = echarts.ComposeOption<GaugeSeriesOption | BaseOptionType>;
type PolyECOption = echarts.ComposeOption<
  | PieSeriesOption
  | BarSeriesOption
  | LineSeriesOption
  | ScatterSeriesOption
  | PictorialBarSeriesOption
  | BaseOptionType
  // | HeatmapECOption
>;
type EchartsOption = echarts.EChartsOption;
type Options =
  | LineECOption
  | BarECOption
  | PieECOption
  | GuageECOption
  | ScatterECOption;
type ChartType =
  | 'bar'
  | 'line'
  | 'pie'
  | 'bar_line'
  | 'scatter'
  | 'heatmap'
  | 'guage';

namespace ChartEvents {
  type MouseEventType =
    | 'click'
    | 'dbclick'
    | 'mousedown'
    | 'mousemove'
    | 'mouseup'
    | 'mouseover'
    | 'mouseout';
  type MouseEvents = {
    [key in MouseEventType as `chart${Capitalize<key>}`]: ECElementEvent;
  };

  interface Events extends MouseEvents {
    selectchanged: SelectChangedPayload;
    highlight: HighlightPayload;
    legendSelected: {
      type: 'legendSelected';
      name: string;
      selected: {
        [name: string]: boolean;
      };
    };
  }
  export type EventType = keyof Events;
}

export {
  BaseOption,
  ChartType,
  LineECOption,
  BarECOption,
  PieECOption,
  GuageECOption,
  ScatterECOption,
  EchartsOption,
  ChartEvents,
  Options,
  PolyECOption,
  PictorialBarECOption,
};
