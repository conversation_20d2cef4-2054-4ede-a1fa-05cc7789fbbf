/**
 * @file editModelCtrl.ts
 * @description 数据关联模型控制器，用于管理数据关联模态框中的图形和数据
 */

import { Graph } from '@antv/x6';
import { register } from '@antv/x6-vue-shape';
import VueNodeComponent from '../shape/TreeNode.vue';
import {
  NodeData,
  GlobalDataMap,
  GlobalParam,
  TreeNode,
  EdgeConnectedEvent,
  GraphController,
} from './types';
import { CUSTOM_NODE_SHAPE, NODE_WIDTH, NODE_HEIGHT } from './constants';
import { createGraph, handleZoom, resetZoom, removeEdge } from './graphUtils';
import { createLeftNode, createRightNode, createGlobalNode } from './nodeUtils';
import { handleEdgeConnected } from './edgeUtils';
import { getNodeList } from './dataUtils';

// 注册 Vue 节点
register({
  shape: CUSTOM_NODE_SHAPE,
  width: NODE_WIDTH,
  height: NODE_HEIGHT,
  component: VueNodeComponent,
});

// 树形数据引用
const treeData = ref<TreeNode[]>([]);

/**
 * 获取节点列表数据
 * @param graph - X6图形实例
 */
const getListArr = async (graph: Graph): Promise<void> => {
  treeData.value = await getNodeList(graph);
};

/**
 * 数据关联模型控制器
 * @returns 数据关联模型控制器方法集合
 */
export default (): GraphController => {
  // 图形实例
  let graph: Graph | null = null;
  // 节点数据引用
  const nodeData = ref<NodeData[]>([]);
  // 全局数据映射引用
  const globalDataMap = ref<GlobalDataMap[]>([]);
  // 缩放比例
  const zoom = ref(1);

  /**
   * 初始化节点
   * @param container - 容器元素
   * @param globalData - 全局数据
   */
  const initNode = (
    container: HTMLElement,
    globalData?: GlobalParam[]
  ): void => {
    // 创建图形实例
    graph = createGraph(container);
    if (treeData.value.length) {
      // 创建左侧节点（当前节点）
      createLeftNode(graph, treeData.value);

      // 创建右侧节点（所有节点）
      createRightNode(graph, treeData.value);
    }

    // 创建全局参数节点（如果有全局参数）
    if (globalData && globalData.length > 0) {
      createGlobalNode(graph, globalData);
    }

    // 监听连线完成事件
    graph.on('edge:connected', (event: EdgeConnectedEvent) => {
      handleEdgeConnected(event, graph!, nodeData, globalDataMap);
    });
  };

  /**
   * 销毁 X6 画布
   */
  const disposeGraph = (): void => {
    if (graph) {
      graph.dispose();
      graph = null;
    }
  };

  /**
   * 移除连线
   * @param edgeId - 连线 ID
   * @param index - 节点数据索引
   */
  const removeCell = (edgeId: string, index: number): void => {
    if (graph) {
      removeEdge(graph, edgeId);
      nodeData.value.splice(index, 1);
    }
  };

  /**
   * 放大画布
   */
  const zoomIn = (): void => {
    if (graph) {
      zoom.value = handleZoom(graph, 0.1);
    }
  };

  /**
   * 缩小画布
   */
  const zoomOut = (): void => {
    if (graph) {
      zoom.value = handleZoom(graph, -0.1);
    }
  };

  /**
   * 重置缩放
   */
  const zoomReset = (): void => {
    if (graph) {
      resetZoom(graph);
      zoom.value = 1;
    }
  };

  return {
    initNode,
    disposeGraph,
    nodeData,
    globalDataMap,
    removeCell,
    getListArr,
    zoomIn,
    zoomOut,
    zoomReset,
  };
};
