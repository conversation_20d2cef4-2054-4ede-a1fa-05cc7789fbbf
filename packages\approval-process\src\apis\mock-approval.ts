import { ApprovalStatus, ApprovalActivityType } from '../types/approval';

import type {
  ApprovalStatistics,
  ApprovalProcess,
  InitiatedApprovalProcess,
  ApprovalActivity,
  ApprovalTrend,
  ApprovalDetail,
  ApprovalTemplate,
} from '../types/approval';

/**
 * 模拟获取审批统计数据
 * @returns 审批统计数据
 */
export const mockGetApprovalStatistics = (): Promise<ApprovalStatistics> => {
  return Promise.resolve({
    pending: 8,
    approved: 42,
    rejected: 5,
    initiated: 23,
  });
};

/**
 * 模拟获取待我审批列表
 * @param params 分页参数
 * @returns 待审批列表
 */
export const mockGetPendingApprovals = (
  params: Global.Pagination
): Promise<Global.ListResponse<ApprovalProcess>> => {
  const mockData: ApprovalProcess[] = [
    {
      id: '1',
      name: '采购申请审批',
      applicant: '张三',
      applyTime: '2023-11-20 10:30',
      status: ApprovalStatus.PENDING,
    },
    {
      id: '2',
      name: '请假申请审批',
      applicant: '李四',
      applyTime: '2023-11-20 09:15',
      status: ApprovalStatus.PENDING,
    },
    {
      id: '3',
      name: '报销申请审批',
      applicant: '王五',
      applyTime: '2023-11-19 16:45',
      status: ApprovalStatus.PENDING,
    },
    {
      id: '4',
      name: '出差申请审批',
      applicant: '赵六',
      applyTime: '2023-11-19 14:20',
      status: ApprovalStatus.PENDING,
    },
  ];

  const { pageNum, pageSize } = params;
  const start = (pageNum - 1) * pageSize;
  const end = start + pageSize;
  const records = mockData.slice(start, end);

  return Promise.resolve({
    records,
    current: pageNum,
    pages: Math.ceil(mockData.length / pageSize),
    size: pageSize,
    total: mockData.length,
  });
};

/**
 * 模拟获取最近审批活动
 * @param limit 限制数量
 * @returns 最近审批活动
 */
export const mockGetRecentActivities = (
  limit: number = 5
): Promise<ApprovalActivity[]> => {
  const mockData: ApprovalActivity[] = [
    {
      user: '张三',
      action: '提交了',
      process: '采购申请审批',
      time: '10分钟前',
      type: ApprovalActivityType.SUBMIT,
    },
    {
      user: '李四',
      action: '审批通过了',
      process: '请假申请审批',
      time: '30分钟前',
      type: ApprovalActivityType.APPROVE,
    },
    {
      user: '王五',
      action: '驳回了',
      process: '报销申请审批',
      time: '1小时前',
      type: ApprovalActivityType.REJECT,
    },
    {
      user: '赵六',
      action: '提交了',
      process: '出差申请审批',
      time: '2小时前',
      type: ApprovalActivityType.SUBMIT,
    },
  ];

  return Promise.resolve(mockData.slice(0, limit));
};

/**
 * 模拟获取审批趋势数据
 * @param days 天数
 * @returns 审批趋势数据
 */
export const mockGetApprovalTrend = (
  days: number = 7
): Promise<ApprovalTrend> => {
  return Promise.resolve({
    dates: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    initiated: [5, 7, 3, 9, 12, 6, 8],
    completed: [3, 5, 2, 8, 10, 5, 7],
  });
};

/**
 * 模拟获取审批模板列表
 * @returns 审批模板列表
 */
export const mockGetApprovalTemplates = (): Promise<ApprovalTemplate[]> => {
  return Promise.resolve([
    {
      id: '1',
      name: '请假申请',
      description: '用于员工请假申请',
      icon: 'calendar',
    },
    {
      id: '2',
      name: '报销申请',
      description: '用于员工报销申请',
      icon: 'money-collect',
    },
    {
      id: '3',
      name: '采购申请',
      description: '用于部门采购申请',
      icon: 'shopping-cart',
    },
    {
      id: '4',
      name: '出差申请',
      description: '用于员工出差申请',
      icon: 'car',
    },
  ]);
};
