import { loadEnv, UserConfig } from 'vite';

export default (port: number, mode: string) => {
  const { VITE_APP_BASE_URL } = loadEnv(mode, process.cwd());
  return {
    server: {
      port,
      host: '0.0.0.0',
      open: true,
      proxy: {
        '/hello': {
          target: 'ws://************:9526',
          changeOrigin: true,
          ws: true,
        },
        [VITE_APP_BASE_URL]: {
          target: 'http://dmxtyj.gw.dev.xnyqt.petrochina',
          // target: 'http://*************:8080/',
          // target: 'http://************:80/',
          changeOrigin: true,
          rewrite: path =>
            path.replace(new RegExp(`^${VITE_APP_BASE_URL}`), ''),
        },
      },
    },
  } as UserConfig;
};
