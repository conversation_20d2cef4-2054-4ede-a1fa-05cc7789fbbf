{"name": "master", "private": true, "version": "0.0.1", "description": "专业软件组件封装平台", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:staging": "vite build --mode staging", "build:stats": "vue-tsc --noEmit && vite build --mode stats", "preview": "vite preview", "format": "prittier . --write", "lint": "eslint --ext .js,.vue  --fix src"}, "dependencies": {"@antv/hierarchy": "^0.6.14", "@antv/layout": "0.3.25", "@antv/x6": "^2.18.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@iconify/json": "catalog:", "@iconify/vue": "catalog:", "@kd/hooks": "catalog:", "@kd/utils": "catalog:", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "core-js": "catalog:", "dayjs": "catalog:", "dialog-async": "catalog:", "echarts": "catalog:", "nprogress": "catalog:", "pinia": "catalog:", "pinia-plugin-persistedstate": "catalog:", "qs": "catalog:", "query-string": "catalog:", "radash": "catalog:", "scene-designer": "workspace:*", "shared": "workspace:*", "vue": "catalog:", "vue-hooks-plus": "catalog:", "vue-router": "catalog:"}}