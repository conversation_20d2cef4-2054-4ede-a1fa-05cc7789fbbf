import { defineStore } from 'pinia';
import {
  getApprovalStatistics,
  getPendingApprovals,
  getInitiatedApprovals,
  getRecentActivities,
  getApprovalTrend,
  approveProcess,
  getApprovalTemplates,
} from '../apis/approval';
import {
  mockGetApprovalStatistics,
  mockGetPendingApprovals,
  mockGetRecentActivities,
  mockGetApprovalTrend,
  mockGetApprovalTemplates,
} from '../apis/mock-approval';
import type {
  ApprovalStatistics,
  ApprovalProcess,
  InitiatedApprovalProcess,
  ApprovalActivity,
  ApprovalTrend,
  ApprovalTemplate,
} from '../types/approval';

// 是否使用模拟数据
const USE_MOCK = true;

export interface ApprovalState {
  statistics: ApprovalStatistics;
  pendingApprovals: ApprovalProcess[];
  initiatedApprovals: InitiatedApprovalProcess[];
  recentActivities: ApprovalActivity[];
  trend: ApprovalTrend;
  templates: ApprovalTemplate[];
  loading: {
    statistics: boolean;
    pendingApprovals: boolean;
    initiatedApprovals: boolean;
    recentActivities: boolean;
    trend: boolean;
    templates: boolean;
  };
}

export const useApprovalStore = defineStore('approval', {
  state: (): ApprovalState => ({
    statistics: {
      pending: 0,
      approved: 0,
      rejected: 0,
      initiated: 0,
    },
    pendingApprovals: [],
    initiatedApprovals: [],
    recentActivities: [],
    trend: {
      dates: [],
      initiated: [],
      completed: [],
    },
    templates: [],
    loading: {
      statistics: false,
      pendingApprovals: false,
      initiatedApprovals: false,
      recentActivities: false,
      trend: false,
      templates: false,
    },
  }),
  actions: {
    async fetchStatistics() {
      this.loading.statistics = true;
      try {
        const result = USE_MOCK
          ? await mockGetApprovalStatistics()
          : await getApprovalStatistics();
        this.statistics = result;
      } catch (error) {
        console.error('获取审批统计数据失败', error);
      } finally {
        this.loading.statistics = false;
      }
    },
    async fetchPendingApprovals(params: { pageNum: number; pageSize: number }) {
      this.loading.pendingApprovals = true;
      try {
        const result = USE_MOCK
          ? await mockGetPendingApprovals(params)
          : await getPendingApprovals(params);
        this.pendingApprovals = result.records;
        return result;
      } catch (error) {
        console.error('获取待审批列表失败', error);
        return null;
      } finally {
        this.loading.pendingApprovals = false;
      }
    },
    async fetchInitiatedApprovals(params: {
      pageNum: number;
      pageSize: number;
    }) {
      this.loading.initiatedApprovals = true;
      try {
        const result = USE_MOCK
          ? await mockGetPendingApprovals(params) // 使用相同的模拟数据
          : await getInitiatedApprovals(params);
        this.initiatedApprovals = result.records as InitiatedApprovalProcess[];
        return result;
      } catch (error) {
        console.error('获取我发起的审批列表失败', error);
        return null;
      } finally {
        this.loading.initiatedApprovals = false;
      }
    },
    async fetchRecentActivities(limit: number = 5) {
      this.loading.recentActivities = true;
      try {
        const result = USE_MOCK
          ? await mockGetRecentActivities(limit)
          : await getRecentActivities(limit);
        this.recentActivities = result;
      } catch (error) {
        console.error('获取最近审批活动失败', error);
      } finally {
        this.loading.recentActivities = false;
      }
    },
    async fetchApprovalTrend(days: number = 7) {
      this.loading.trend = true;
      try {
        const result = USE_MOCK
          ? await mockGetApprovalTrend(days)
          : await getApprovalTrend(days);
        this.trend = result;
      } catch (error) {
        console.error('获取审批趋势数据失败', error);
      } finally {
        this.loading.trend = false;
      }
    },
    async fetchApprovalTemplates() {
      this.loading.templates = true;
      try {
        const result = USE_MOCK
          ? await mockGetApprovalTemplates()
          : await getApprovalTemplates();
        this.templates = result;
      } catch (error) {
        console.error('获取审批模板列表失败', error);
      } finally {
        this.loading.templates = false;
      }
    },
    async approveProcess(data: {
      id: string;
      action: 'approve' | 'reject';
      comment?: string;
    }) {
      try {
        // 如果使用模拟数据，直接返回成功
        const result = USE_MOCK ? true : await approveProcess(data);
        if (result) {
          // 更新待审批列表
          this.pendingApprovals = this.pendingApprovals.filter(
            item => item.id !== data.id
          );
          // 更新统计数据
          if (data.action === 'approve') {
            this.statistics.pending--;
            this.statistics.approved++;
          } else {
            this.statistics.pending--;
            this.statistics.rejected++;
          }
        }
        return result;
      } catch (error) {
        console.error('审批流程失败', error);
        return false;
      }
    },
  },
});

export default useApprovalStore;
