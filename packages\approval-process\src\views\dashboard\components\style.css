/* 卡片基础样式 */
.stat-card {
  position: relative;
  overflow: hidden;
  min-height: 120px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* 卡片内容布局 */
.stat-card-content {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 16px;
}

/* 图标样式 */
.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  margin-right: 16px;
  color: white;
  font-size: 20px;
}

/* 信息区域样式 */
.stat-info {
  display: flex;
  flex-direction: column;
}

/* 标题样式 */
.stat-title {
  font-size: 14px;
  color: white;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 数值样式 */
.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: white;
}

/* 底部样式 */
.stat-footer {
  padding: 8px 16px;
  text-align: right;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

/* 卡片颜色变体 */
.stat-card-blue {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-100));
}

.stat-card-green {
  background: linear-gradient(135deg, #00b96b, #34a853);
}

.stat-card-red {
  background: linear-gradient(135deg, #ff4d4f, #ea4335);
}

.stat-card-purple {
  background: linear-gradient(135deg, #9c27b0, #ba68c8);
}

/* 波纹效果 */
.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.1);
  transform-origin: top right;
  transform: scale(0);
  border-radius: 0 0 0 100%;
  transition: all 0.5s ease-out;
  z-index: 1;
}

.stat-card:hover::before {
  width: 200%;
  height: 200%;
  transform: scale(1);
}

/* 悬停效果 */
.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* 确保内容在波纹上方 */
.stat-card-content,
.stat-footer {
  position: relative;
  z-index: 2;
}
