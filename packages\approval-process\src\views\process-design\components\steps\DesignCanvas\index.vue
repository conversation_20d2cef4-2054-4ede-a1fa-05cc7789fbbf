<template>
  <div class="h-full">
    <!-- 工作流画布 -->
    <div ref="canvasContainer" class="h-full relative"></div>

    <!-- 画布控制按钮 -->
    <div class="canvas-controls">
      <a-button-group>
        <a-tooltip title="放大">
          <a-button @click="handleZoomIn">
            <template #icon><PlusOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="缩小">
          <a-button @click="handleZoomOut">
            <template #icon><MinusOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="重置视图">
          <a-button @click="handleResetView">
            <template #icon><ReloadOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="自定义布局">
          <a-button @click="handleAutoLayout">
            <template #icon><PartitionOutlined /></template>
          </a-button>
        </a-tooltip>
      </a-button-group>
      <div class="canvas-tip">
        <a-tooltip title="按住Alt键拖动可平移画布">
          <QuestionCircleOutlined />
        </a-tooltip>
      </div>
    </div>

    <!-- 右侧设置面板 -->
    <a-drawer
      :open="!!currentNode"
      title="节点设置"
      placement="right"
      :width="350"
      :closable="true"
      @close="() => (currentNode = undefined)"
    >
      <NodeSetting v-if="currentNode" :node="currentNode" :graph="graph" />
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { useProcessContext } from '../../../useProcessContext';
import { createApprovalWorkflow } from './useApprovalWorkflow';
import NodeSetting from './components/NodeSetting/index.vue';
import { generateUUID } from 'shared/utils';
import { message } from 'ant-design-vue';
import { ApprovalWorkflowGraph } from './ApprovalWorkflowGraph';
import PlusOutlined from '~icons/ant-design/plus-outlined';
import MinusOutlined from '~icons/ant-design/minus-outlined';
import ReloadOutlined from '~icons/ant-design/reload-outlined';
import PartitionOutlined from '~icons/ant-design/partition-outlined';
import NodeIndexOutlined from '~icons/ant-design/node-index-outlined';
import QuestionCircleOutlined from '~icons/ant-design/question-circle-outlined';

// 获取流程上下文
const { processData } = useProcessContext();

// 创建审批流程上下文
const { initWorkflow, currentNode } = createApprovalWorkflow();

// 画布容器引用
const canvasContainer = ref<HTMLElement>();

// 图形实例
const graph = shallowRef<ApprovalWorkflowGraph>();

// 初始化画布
onMounted(() => {
  if (canvasContainer.value) {
    // 初始化工作流图形
    graph.value = initWorkflow(canvasContainer.value);

    // 如果流程数据中已有节点和边，则加载它们
    if (processData.value.flow.nodes.length > 0) {
      graph.value.fromJSON({
        nodes: processData.value.flow.nodes,
        edges: processData.value.flow.edges,
      });

      // 为所有已有的边添加工具
      const edges = graph.value.getEdges();
      edges.forEach(edge => {
        graph.value?.addEdgeTools(edge);
      });
    } else {
      // 否则创建默认的起始节点（提交节点）
      createDefaultWorkflow();
    }
  }
});

// 处理放大画布
const handleZoomIn = () => {
  if (graph.value) {
    graph.value.zoomIn();
  }
};

// 处理缩小画布
const handleZoomOut = () => {
  if (graph.value) {
    graph.value.zoomOut();
  }
};

// 处理重置视图
const handleResetView = () => {
  if (graph.value) {
    graph.value.resetView();
  }
};

// 处理自定义布局
const handleAutoLayout = () => {
  if (graph.value) {
    graph.value.doWorkflowLayout();
  }
};

// 创建默认工作流
const createDefaultWorkflow = () => {
  if (!graph.value) return;

  // 创建提交节点
  const submitNode = graph.value.addNode({
    id: generateUUID(),
    shape: 'approval-node',
    x: 300,
    y: 100,
    width: 180,
    height: 80,
    data: {
      type: 'submit',
      title: '发起人',
      description: '提交申请',
    },
  });

  // 创建审批节点
  const approvalNode = graph.value.addNode({
    id: generateUUID(),
    shape: 'approval-node',
    x: 300,
    y: 250,
    width: 180,
    height: 80,
    data: {
      type: 'approval',
      title: '审批',
      description: '审批人：请选择审批人',
      approvers: [],
    },
  });

  // 创建结束节点
  const endNode = graph.value.addNode({
    id: generateUUID(),
    shape: 'approval-node',
    x: 300,
    y: 400,
    width: 180,
    height: 80,
    data: {
      type: 'end',
      title: '结束',
      description: '流程结束',
    },
  });

  // 添加提交节点到审批节点的连接线
  graph.value.addEdge({
    shape: 'approval-edge',
    source: { cell: submitNode, port: 'bottom' },
    target: { cell: approvalNode, port: 'top' },
  });

  // 添加审批节点到结束节点的连接线
  graph.value.addEdge({
    shape: 'approval-edge',
    source: { cell: approvalNode, port: 'bottom' },
    target: { cell: endNode, port: 'top' },
  });
  graph.value.centerContent();
  // 保存到流程数据
  // saveGraphData();
};
</script>

<style>
.x6-node-selected rect {
  stroke: #40cfa0;
  stroke-width: 2px;
}

.x6-edge:hover path {
  stroke: #40cfa0;
  stroke-width: 2px;
}

.x6-port-body {
  fill: #fff;
  stroke: #40cfa0;
  stroke-width: 1px;
}

.x6-port-body:hover {
  fill: #40cfa0;
  stroke: #40cfa0;
  stroke-width: 2px;
}

/* 画布控制按钮样式 */
.canvas-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.canvas-tip {
  margin-left: 10px;
  color: #999;
  cursor: help;
}

.canvas-tip:hover {
  color: #40cfa0;
}
</style>
