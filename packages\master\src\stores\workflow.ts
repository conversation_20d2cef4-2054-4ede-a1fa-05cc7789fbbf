import { getToolListByPage, getListType } from '@/master/apis/tools';
import { message } from 'ant-design-vue';
import type { Tool, ToolCategory } from '@/master/types/tool';
import { NODE_TYPE } from '../views/workbench/constants';

export default defineStore('workflow', {
  state: () => ({
    componentsMap: new Map<ToolCategory, Tool[]>(),
    startNode: undefined as undefined | Tool,
  }),
  actions: {
    async initTools() {
      try {
        const res = await getListType({});
        for (let t of res.records) {
          await this.initComponentsByType({
            sysDesComponentTypeId: t.sysDesComponentTypeId,
            name: t.name,
            pageNum: 1,
            pageSize: 1000,
            componentStatus: '1',
          });
        }
      } catch (e: any) {
        message.error(e.message);
      }
    },
    async initComponentsByType(data: any) {
      const res = await getToolListByPage(data);
      this.componentsMap.set(data, res.records);
      if (data.name === '通用组件') {
        this.startNode = res.records.find(
          t => t.componentType === NODE_TYPE.START
        );
      }
    },
  },
  getters: {
    tools: state => [...state.componentsMap.keys()],
  },
});
