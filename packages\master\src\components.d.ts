/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AMentions: typeof import('ant-design-vue/es')['Mentions']
    AMentionsOption: typeof import('ant-design-vue/es')['MentionsOption']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    AntDesignEyeOutlined: typeof import('~icons/ant-design/eye-outlined')['default']
    AntDesignSaveOutlined: typeof import('~icons/ant-design/save-outlined')['default']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AResult: typeof import('ant-design-vue/es')['Result']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpaceCompact: typeof import('ant-design-vue/es')['Compact']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASteps: typeof import('ant-design-vue/es')['Steps']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATypographyTitle: typeof import('ant-design-vue/es')['TypographyTitle']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    GridiconsAdd: typeof import('~icons/gridicons/add')['default']
    HugeiconsPropertyEdit: typeof import('~icons/hugeicons/property-edit')['default']
    Icon: typeof import('~icons/ic/on')['default']
    IconMaker: typeof import('~icons/ic/on-maker')['default']
    MaterialSymbolsDeleteOutline: typeof import('~icons/material-symbols/delete-outline')['default']
    MingcuteDotGridFill: typeof import('~icons/mingcute/dot-grid-fill')['default']
    MinusCircleOutlined: typeof import('~icons/mi/nus-circle-outlined')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
