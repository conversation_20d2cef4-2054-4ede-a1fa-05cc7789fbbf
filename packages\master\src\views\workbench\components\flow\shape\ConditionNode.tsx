/**
 * @file ConditionNode.tsx
 * @description 条件判断节点组件，用于在工作流图中显示条件判断节点
 */

import { Graph, Node } from '@antv/x6';
import { NODE_STATUS } from '../../../constants';
import Reload from '~icons/ant-design/reload-outlined';
import Success from '~icons/ant-design/check-circle-outlined';
import Failed from '~icons/ant-design/close-circle-outlined';
import style from './conditionNode.module.css';
import { Empty, Space } from 'ant-design-vue';
import type { Condition } from '@/master/types/flow';
/**
 * 条件判断节点组件
 */
export default defineComponent({
  name: 'ConditionNode',
  props: {
    /** 节点实例 */
    node: {
      type: Object as PropType<Node>,
      required: true,
    },
    /** 图形实例 */
    graph: Object as PropType<Graph>,
  },
  setup(props) {
    const isCollapsed = ref(false);
    // 节点数据
    const data = ref(props.node?.getData() || {});
    const originSize = ref(props.node.getSize());
    // 当前条件项
    const conditions = ref<Condition[]>([]);

    // 监听节点数据变化
    props.node!.on('change:data', ({ current }) => {
      data.value = current;
      conditions.value = current.conditions || [];
    });

    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value;
      const size = props.node?.getSize()!;
      props.node?.resize(
        size.width,
        isCollapsed.value ? 40 : originSize.value.height
      );
    };

    return { data, conditions, isCollapsed, toggleCollapse };
  },

  render() {
    // 获取条件信息
    const conditions = this.data?.conditions || [];
    return (
      <div
        class={[
          style.conditionNode,
          this.data.isSelected ? style.selected : '',
        ]}
      >
        <div class={style.conditionContent}>
          <div class={style.conditionNodeHeader}>
            <span class={style.nodeName}>条件</span>
            <Space>
              {/* <div class={style.nodeHeaderIcon}>
                <BxCaretDown
                  class={this.isCollapsed ? style.transform : null}
                  onClick={this.toggleCollapse}
                />
              </div> */}
              <div>
                {/* 运行中状态 */}
                {this.data?.status === NODE_STATUS.RUNNING && (
                  <Reload style="animation: spin 1s infinite linear" />
                )}
                {/* 成功状态 */}
                {this.data?.status === NODE_STATUS.SUCCESS && (
                  <Success style={{ color: 'oklch(0.768 0.233 130.85)' }} />
                )}
                {/* 失败状态 */}
                {this.data?.status === NODE_STATUS.FAILED && (
                  <Failed style={{ color: 'oklch(0.637 0.237 25.331)' }} />
                )}
              </div>
            </Space>
          </div>
          <div class={style.conditionNodeBody}>
            {conditions.length > 0 ? (
              conditions.map((item: Condition) => (
                <div key={item.id} class={style.conditionItem}>
                  <span class={style.ifLabel}>if</span>
                  <span class={style.conditionExpression}>
                    {item.condition}
                  </span>
                </div>
              ))
            ) : (
              <Empty imageStyle={{ height: '25px' }} />
            )}
          </div>
        </div>
        <span class={style.businessNodeName}>{this.data?.componentName}</span>
      </div>
    );
  },
});
