<template>
  <div class="stat-card" :class="colorClass" @click="$emit('click')">
    <div class="stat-card-content">
      <div class="stat-icon">
        <component :is="icon" />
      </div>
      <div class="stat-info">
        <div class="stat-title">{{ title }}</div>
        <div class="stat-value">{{ value }}</div>
      </div>
    </div>
    <div class="stat-footer">
      <span>点击查看详情</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import './style.css';

const props = defineProps<{
  title: string;
  value: number;
  type: 'primary' | 'success' | 'error' | 'info';
  icon: any;
}>();

const colorClass = computed(() => {
  const colorMap = {
    primary: 'stat-card-blue',
    success: 'stat-card-green',
    error: 'stat-card-red',
    info: 'stat-card-purple',
  };
  return colorMap[props.type];
});

defineEmits<{
  (e: 'click'): void;
}>();
</script>
