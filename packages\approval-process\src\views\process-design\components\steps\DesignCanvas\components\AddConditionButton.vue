<template>
  <a-button shape="round" type="primary" @click="handleClick">
    <PlusOutlined />
    <span>添加条件</span>
  </a-button>
</template>

<script setup lang="ts">
import PlusOutlined from '~icons/ant-design/plus-outlined';
import type { Graph, Node } from '@antv/x6';

const props = defineProps<{
  node?: Node;
  graph?: Graph;
}>();

// 处理点击事件
const handleClick = (e: MouseEvent) => {
  e.stopPropagation();

  // 如果有 node 和 graph 属性，直接调用 graph 的 handleAddBranch 方法
  if (props.graph && typeof props.graph.handleAddBranch === 'function') {
    props.graph.handleAddBranch(props.node);
  }
};
</script>
