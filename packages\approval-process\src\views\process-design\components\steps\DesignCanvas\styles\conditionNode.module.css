.conditionNode {
  width: 100%;
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
}

.conditionNode.selected {
  border-color: #40cfa0;
  box-shadow: 0 0 0 2px rgba(64, 207, 160, 0.2);
}

.nodeHeader {
  height: 32px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f0f7ff;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
}

.nodeTitle {
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}

.nodeBody {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nodeDescription {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.nodeAction {
  color: #1890ff;
  cursor: pointer;
}

.nodeAction:hover {
  color: #40cfa0;
}

.addBranchButton {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}
