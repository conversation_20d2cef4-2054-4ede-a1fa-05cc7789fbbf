import { formatConfig } from '@/scene-designer/utils';

const workbenchStore = defineStore('workbench', {
  state: () => ({
    isDragging: false,
    draggingWidget: '',
    mode: 'design' as 'design' | 'preview',
    appStyleSchema: {
      backgroundColor: {
        value: '#fff',
        label: '背景色',
        comp: 'color-picker',
      },
      fontSize: {
        value: 14,
        label: '字体大小',
        comp: 'a-slider',
        compProps: {
          min: 12,
          max: 36,
          step: 1,
        },
      },
      backgroundImage: {
        value: undefined,
        comp: 'image-select',
        label: '背景图片',
      },
    } as Record<string, any>,
    injectSource: [] as any[],
  }),
  actions: {
    setIsDragging(isDragging: boolean) {
      this.isDragging = isDragging;
    },
    setDraggingWidget(widget: string) {
      this.draggingWidget = widget;
    },
    setMode(mode: 'preview' | 'design') {
      this.mode = mode;
    },
    setAppStyle(key: string, value: any) {
      this.appStyleSchema[key].value = value;
    },
    setInjectSource(value: any[]) {
      this.injectSource = value;
    },
  },
  getters: {
    appStyle: state => formatConfig(state.appStyleSchema),
  },
});

export default workbenchStore;
