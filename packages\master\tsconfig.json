{"extends": "../../tsconfig.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/scene-designer/*": ["../../packages/scene-designer/src/*"], "@/master/*": ["../../packages/master/src/*"], "@/approval-process/*": ["../../packages/approval-process/src/*"]}, "types": ["vite/client", "unplugin-icons/types/vue", "../global.d.ts"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/auto-imports.d.ts", "src/components.d.ts"]}