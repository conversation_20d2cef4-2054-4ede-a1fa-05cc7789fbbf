<template>
  <div class="h-full">
    <div class="grid grid-cols-4 gap-4 mb-4">
      <!-- 统计卡片 -->
      <StatCard
        title="待我审批"
        :value="statistics.pending"
        type="primary"
        :icon="ClockCircleOutlined"
        @click="handleViewMore"
      />

      <StatCard
        title="已审批"
        :value="statistics.approved"
        type="success"
        :icon="CheckCircleOutlined"
        @click="handleViewApproved"
      />

      <StatCard
        title="已拒绝"
        :value="statistics.rejected"
        type="error"
        :icon="CloseCircleOutlined"
        @click="handleViewRejected"
      />

      <StatCard
        title="我发起的"
        :value="statistics.initiated"
        type="info"
        :icon="FileTextOutlined"
        @click="handleViewMyApprovals"
      />
    </div>

    <div class="grid grid-cols-3 gap-4">
      <!-- 待我审批列表 -->
      <PendingApprovalTable
        title="待我审批"
        :data-source="pendingApprovals"
        :columns="pendingColumns"
        @view-more="handleViewMore"
        @approve="handleApprove"
        @view-detail="handleViewDetail"
      />

      <!-- 审批状态统计图表 -->
      <ChartCard title="审批状态统计" type="pie" :options="pieOptions" />

      <!-- 最近审批活动 -->
      <ApprovalTimeline title="最近审批活动" :activities="recentActivities" />

      <!-- 审批趋势图表 -->
      <ChartCard title="审批趋势" type="line" :options="lineOptions" />

      <!-- 快速操作 -->
      <QuickActions
        title="快速操作"
        :actions="quickActions"
        @action="handleQuickAction"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import useApprovalStore from '@/approval-process/stores/approval';
import ClockCircleOutlined from '~icons/ant-design/clock-circle-outlined';
import CheckCircleOutlined from '~icons/ant-design/check-circle-outlined';
import CloseCircleOutlined from '~icons/ant-design/close-circle-outlined';
import FileTextOutlined from '~icons/ant-design/file-text-outlined';
import FormOutlined from '~icons/ant-design/form-outlined';
import ProfileOutlined from '~icons/ant-design/profile-outlined';
import AppstoreOutlined from '~icons/ant-design/appstore-outlined';
import BarChartOutlined from '~icons/ant-design/bar-chart-outlined';

import {
  StatCard,
  PendingApprovalTable,
  ApprovalTimeline,
  QuickActions,
  ChartCard,
} from './components';
import { getPendingColumns } from './utils';
import { getPieOptions, getLineOptions } from './chartOptions';

// 用户信息
const approvalStore = useApprovalStore();
const router = useRouter();

// 统计数据
const { statistics, pendingApprovals, recentActivities } = approvalStore;

// 待审批列表列定义
const pendingColumns = getPendingColumns();

// 图表配置
const pieOptions = computed(() => getPieOptions(statistics));
const lineOptions = computed(() => getLineOptions(approvalStore.trend));

// 快速操作配置
const quickActions = [
  {
    key: 'initiate',
    title: '发起审批',
    icon: FormOutlined,
    color: 'blue',
  },
  {
    key: 'myApprovals',
    title: '我的审批',
    icon: ProfileOutlined,
    color: 'green',
  },
  {
    key: 'templates',
    title: '审批模板',
    icon: AppstoreOutlined,
    color: 'purple',
  },
  {
    key: 'statistics',
    title: '审批统计',
    icon: BarChartOutlined,
    color: 'orange',
  },
];

// 处理快速操作
const handleQuickAction = (key: string) => {
  switch (key) {
    case 'initiate':
      handleInitiateApproval();
      break;
    case 'myApprovals':
      handleViewMyApprovals();
      break;
    case 'templates':
      handleViewTemplates();
      break;
    case 'statistics':
      handleViewStatistics();
      break;
  }
};

// 处理查看更多
const handleViewMore = () => {
  router.push({
    path: '/process-manage',
    query: { type: 'pending' },
  });
};

// 处理查看已审批
const handleViewApproved = () => {
  router.push({
    path: '/process-manage',
    query: { type: 'approved' },
  });
};

// 处理查看已拒绝
const handleViewRejected = () => {
  router.push({
    path: '/process-manage',
    query: { type: 'rejected' },
  });
};

// 处理审批
const handleApprove = (record: any) => {
  router.push({
    path: '/process-design',
    query: { id: record.id, mode: 'approve' },
  });
};

// 处理查看详情
const handleViewDetail = (record: any) => {
  router.push({
    path: '/process-design',
    query: { id: record.id, mode: 'view' },
  });
};

// 处理发起审批
const handleInitiateApproval = () => {
  router.push('/process-design');
};

// 处理查看我的审批
const handleViewMyApprovals = () => {
  router.push({
    path: '/process-manage',
    query: { type: 'initiated' },
  });
};

// 处理查看模板
const handleViewTemplates = () => {
  router.push({
    path: '/process-design',
    query: { mode: 'template' },
  });
};

// 处理查看统计
const handleViewStatistics = () => {
  message.info('审批统计功能正在开发中');
};

// 初始化数据
onMounted(async () => {
  try {
    // 获取统计数据
    await approvalStore.fetchStatistics();

    // 获取待审批列表
    await approvalStore.fetchPendingApprovals({ pageNum: 1, pageSize: 4 });

    // 获取最近审批活动
    await approvalStore.fetchRecentActivities(4);

    // 获取审批趋势
    await approvalStore.fetchApprovalTrend(7);
  } catch (error) {
    message.error('加载数据失败，请刷新页面重试');
  }
});
</script>
