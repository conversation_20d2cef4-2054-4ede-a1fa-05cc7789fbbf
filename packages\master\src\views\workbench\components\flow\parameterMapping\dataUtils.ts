/**
 * @file dataUtils.ts
 * @description Utility functions for data processing
 */

import { Graph } from '@antv/x6';
import { NODE_TYPE } from '../../../constants';
import { TreeNode, TreeNodeChild } from './types';

/**
 * Extracts parameters from node data
 * @param nodeData - The node data
 * @param nodeId - The node ID
 * @returns The extracted parameters
 */
function extractParams(nodeData: any, nodeId: string): TreeNodeChild[] {
  const inputParams = JSON.parse(nodeData.inputParams || '[]').map(
    (param: any) => ({
      ...param,
      nodeId,
    })
  );

  const outputParams = JSON.parse(nodeData.outputParams || '[]').map(
    (param: any) => ({
      ...param,
      nodeId,
    })
  );

  return [...inputParams, ...outputParams];
}

/**
 * Creates a tree node from node data
 * @param nodeData - The node data
 * @param nodeId - The node ID
 * @returns The created tree node
 */
function createTreeNode(nodeData: any, nodeId: string): TreeNode {
  return {
    componentname: nodeData.componentName,
    componenttype: nodeData.componentType,
    name: nodeData.sysDesComponentId,
    label: nodeData.componentName,
    sysdescomponentid: nodeData.sysDesComponentId,
    children: extractParams(nodeData, nodeId),
  };
}

/**
 * Gets the list of nodes from the graph
 * @param graph - The graph instance
 * @returns A promise that resolves to the tree data
 */
export async function getNodeList(graph: Graph): Promise<TreeNode[]> {
  const nodeList: TreeNode[] = [];

  // Get all software nodes
  const softwareNodes = graph
    .getNodes()
    .filter(node => node.getData()?.componentType === NODE_TYPE.SOFT);

  softwareNodes.forEach(node => {
    const nodeId = node.id;
    const nodeData = node.getData();
    const treeNode = createTreeNode(nodeData, nodeId);
    nodeList.push(treeNode);
  });

  return nodeList;
}
