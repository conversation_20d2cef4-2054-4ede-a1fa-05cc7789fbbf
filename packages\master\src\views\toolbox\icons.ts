/**
 * @file icons.ts
 * @description 工具箱图标配置文件
 * 统一管理所有工具组件的图标资源
 */

// 专业软件图标
import PIPESIM from '~icons/my/pipeSim';
import GAP from '~icons/my/Gap';
import HYSIS from '~icons/my/Hysis';
import LEDAFLOW from '~icons/my/LedaFlow';
import KSPICE from '~icons/my/KSpice';
import SEPSIM from '~icons/my/SEPSIM';
import TGNET from '~icons/my/TGNET';

// 工作流节点图标
import GROUP from '~icons/my/group';
import LOOP from '~icons/my/loop';
import CONDITION from '~icons/my/condition';
import DATA from '~icons/my/data';
import START from '~icons/my/start';
import END from '~icons/my/end';
import PLACEHOLDER from '~icons/my/placeholder';

// 通用图标
import OUTPUT from '~icons/material-symbols/output';
import INPUT from '~icons/material-symbols/input';

/**
 * 图标映射表
 * 支持多种命名方式的图标查找
 */
const iconMap = {
  // 专业软件图标
  PIPESIM,
  'IPM GAP': GAP,
  GAP,
  HYSIS,
  LEDAFLOW,
  SEPSIM,
  TGNET,
  KSPICE,

  // 工作流节点图标
  GROUP,
  LOOP,
  CONDITION,
  DATA,
  START,
  END,
  PLACEHOLDER,

  // 通用图标
  INPUT,
  OUTPUT,
} as const;

/**
 * 图标类型定义
 */
export type IconKey = keyof typeof iconMap;

/**
 * 图标代理对象
 * 提供灵活的图标查找机制，支持大小写不敏感的查找
 */
const icons = new Proxy(iconMap, {
  get(target, prop: string | symbol) {
    if (typeof prop !== 'string') {
      return undefined;
    }

    // 直接匹配
    if (prop in target) {
      return target[prop as IconKey];
    }

    // 大写匹配
    const upperProp = prop.toUpperCase() as IconKey;
    if (upperProp in target) {
      return target[upperProp];
    }

    // 小写匹配
    const lowerProp = prop.toLowerCase() as IconKey;
    if (lowerProp in target) {
      return target[lowerProp];
    }

    // 未找到图标时返回 undefined
    return undefined;
  },
});

/**
 * 获取图标组件
 * @param iconName 图标名称
 * @returns 图标组件或 undefined
 */
export const getIcon = (iconName: string): any => {
  return icons[iconName as IconKey];
};

/**
 * 检查图标是否存在
 * @param iconName 图标名称
 * @returns 是否存在该图标
 */
export const hasIcon = (iconName: string): boolean => {
  return getIcon(iconName) !== undefined;
};

/**
 * 获取所有可用的图标名称
 * @returns 图标名称数组
 */
export const getAvailableIcons = (): string[] => {
  return Object.keys(iconMap);
};

export default icons;
