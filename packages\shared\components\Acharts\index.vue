<template>
  <div ref="chartRef" class="h-full"></div>
</template>
<script setup lang="ts">
import { type PropType, toRefs, shallowRef, onMounted, watch } from 'vue';
import * as echarts from 'echarts/core';
import { useCharts, type ChartType, type ChartEvents } from './useCharts';

interface EventEmitsType {
  <T extends ChartEvents.EventType>(
    e: `${T}`,
    event: ChartEvents.Events[Uncapitalize<T>]
  ): void;
}

defineOptions({
  name: 'ACharts',
});

const props = defineProps({
  type: {
    type: String as PropType<ChartType>,
    default: 'bar',
  },
  theme: {
    type: String as PropType<'light' | 'dark' | string>,
    default: 'light',
  },
  options: {
    type: Object as PropType<echarts.EChartsCoreOption>,
    default: () => ({}),
  },
  loading: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  renderer: {
    type: String as PropType<'svg' | 'canvas'>,
    default: 'canvas',
  },
});

// 定义事件发射器
defineEmits<EventEmitsType>();

// 将props转换为refs
const { type, options, theme, loading, renderer } = toRefs(props);

// 创建chartRef并使用useCharts
const chartRef = shallowRef();
const { charts, setOptions, initChart } = useCharts({
  type,
  el: chartRef,
  theme,
  options,
  renderer,
});

// 挂载时，等待200毫秒，初始化图表，设置options
onMounted(async () => {
  await initChart();
  setOptions(options.value);
  if (loading.value) {
    charts.value?.showLoading({ text: '' });
  }
});

// 监听options，每200毫秒更新一次
watch(
  () => options.value,
  value => {
    setOptions(value);
  },
  {
    deep: true,
  }
);

watch(
  () => loading.value,
  value => {
    if (value) {
      charts.value?.showLoading({ text: '' });
    } else {
      charts.value?.hideLoading();
    }
  }
);

// 导出charts
defineExpose({
  $charts: charts,
});
</script>
