const _resources = import.meta.glob<Widget.ResourceConfig>(
  '@/scene-designer/components/**/resource.ts',
  {
    import: 'default',
    eager: true,
  }
);

const grouped = Object.values(_resources).reduce(
  (acc, cur) => {
    if (!acc[cur.type]) {
      acc[cur.type] = [];
    }
    acc[cur.type].push(cur);
    return acc;
  },
  {} as Record<keyof typeof Widget.WidgetType, Widget.ResourceConfig[]>
);

const resources = {
  auxiliary: {
    title: '辅助组件',
    widgets: grouped.AUXILIARY,
  },
  basic: {
    title: '基础组件',
    widgets: grouped.BASIC,
  },
  layout: {
    title: '布局组件',
    widgets: grouped.LAYOUT,
  },
  form: {
    title: '表单组件',
    widgets: grouped.FORM,
  },
};

export default resources;
