<template>
  <div class="header flex justify-between items-center px-4">
    <div>
      <a-space>
        <a @click="handleNavigateBack">
          <AntDesignLeftOutlined />
        </a>
        <div class="font-bold text-xl">
          <span>{{ workflowParams.wfName }}</span>
        </div>
      </a-space>
    </div>
    <a-space>
      <a-button
        shape="round"
        type="primary"
        :loading="isSaving"
        @click="handleSave"
      >
        <template #icon>
          <AntDesignSaveOutlined />
        </template>
        保存
      </a-button>
      <a-button shape="round" :disabled="!isWorkflowSaved" @click="handleRun">
        <template #icon>
          <CodIconDebugStart />
        </template>
        自动运行
      </a-button>
      <a-button
        shape="round"
        :disabled="!isWorkflowSaved"
        @click="handleSingleStepDebug"
      >
        <template #icon>
          <CodIconDebugAlt />
        </template>
        单步调试
      </a-button>
    </a-space>
  </div>
  <div class="workflow-container">
    <CustomToolPanel />
    <CustomWorkflowCanvas />
    <CustomParametersPanel />
  </div>
</template>

<script setup lang="tsx">
/**
 * @file CustomWorkbench.vue
 * @description 工作流设计器主界面组件
 */
import type {
  ConditionNodeData,
  DagNode,
  RunningInfoParams,
  WorkflowNodeData,
} from '@/master/types/flow';
import type { Node, Edge } from '@antv/x6';
import { message } from 'ant-design-vue';
import { showDialog } from 'dialog-async';
import CustomToolPanel from './components/tools/index.vue';
import CustomParametersPanel from './components/params/CustomParametersPanel.vue';
import CustomWorkflowCanvas from './components/flow/index.vue';
import AntDesignLeftOutlined from '~icons/ant-design/left-outlined';
import AntDesignSaveOutlined from '~icons/ant-design/save-outlined';
import CodIconDebugStart from '~icons/codicon/debug-start';
import CodIconDebugAlt from '~icons/codicon/debug-alt';
import { createWorkflowGraph } from './components/flow/useWorkFlowGraph';
import ParametersInputModal from './ParametersInputModal.vue';
import {
  runWorkflow,
  saveOrUpdate,
  getRunningInfo,
  startSingleStepDebug,
  nextStepDebug,
} from '@/master/apis/flow';

import {
  APP_ID,
  FLOW_STATUS,
  GRAPH_SHAPE,
  NODE_STATUS,
  NODE_TYPE,
} from './constants';
import { Tool } from '@/master/types/tool';

// 路由相关
const router = useRouter();
const route = useRoute();
const isSaving = ref(false);

// 工作流状态
const workflowPollingId = ref<number>(0);
const singleStepDebugInstanceId = ref<string>('');
const { workflow, workflowParams } = createWorkflowGraph();
const currentStoppedNode = ref<DagNode>();

// 计算属性
const isWorkflowSaved = computed(() => !!workflowParams.value.id);

/**
 * 返回上一页
 */
const handleNavigateBack = (): void => {
  router.push('/dashboard');
};

/**
 * 轮询获取工作流运行信息
 * @param {RunningInfoParams} params - 运行信息参数
 */
const intervalGetRunningInfo = (params: RunningInfoParams): void => {
  let isPollingActive = true;

  // 清除之前的轮询（如果存在）
  if (workflowPollingId.value !== 0) {
    clearInterval(workflowPollingId.value);
    workflowPollingId.value = 0;
  }

  // 更新节点状态的函数
  const updateNodeStatus = (): void => {
    if (!isPollingActive) return;

    getRunningInfo(params)
      .then(detail => {
        const nodes = detail.peworkflowDAG.nodes;
        workflow.value?.updateNodeStatus(nodes);

        // 如果工作流不再运行，停止轮询
        if (detail.status !== FLOW_STATUS.RUNNING) {
          isPollingActive = false;
          clearInterval(workflowPollingId.value);
          workflowPollingId.value = 0;
        }
      })
      .catch(error => {
        console.error('获取工作流运行信息失败:', error);
        isPollingActive = false;
        clearInterval(workflowPollingId.value);
        workflowPollingId.value = 0;
      });
  };

  // 立即执行一次
  updateNodeStatus();

  // 设置轮询间隔
  workflowPollingId.value = window.setInterval(updateNodeStatus, 4000);
};

/**
 * 单步获取工作流运行信息
 * @param {RunningInfoParams} params - 运行信息参数
 */
const singleGetRunningInfo = (params: RunningInfoParams): void => {
  // 清除之前的轮询（如果存在）
  if (workflowPollingId.value !== 0) {
    clearInterval(workflowPollingId.value);
    workflowPollingId.value = 0;
  }

  /**
   * 轮询获取工作流运行状态并处理节点
   */
  const pollRunningInfo = (): void => {
    getRunningInfo(params)
      .then(detail => {
        const nodes = detail.peworkflowDAG.nodes;
        workflow.value?.updateNodeStatus(nodes);

        // 检查是否有运行中的节点
        const runningNodes = nodes.filter(
          node => node.status === NODE_STATUS.RUNNING
        );

        // 获取所有状态为STOPPED的节点
        const stoppedNodes = nodes.filter(
          node => node.status === NODE_STATUS.STOPPED
        );

        // 如果没有停止的节点，继续轮询
        if (stoppedNodes.length === 0) return;

        // 获取图形中对应的节点对象
        const graphStoppedNodes = stoppedNodes.map(node => {
          const graphNode = workflow.value!.getCellById(node.pageNodeId);
          return { dagNode: node, graphNode };
        });

        // 分类节点：GROUP/END类型和其他类型
        const groupOrEndNodes = graphStoppedNodes.filter(item =>
          [NODE_TYPE.GROUP, NODE_TYPE.END].includes(
            item.graphNode.data.componentType
          )
        );
        const otherNodes = graphStoppedNodes.filter(
          item =>
            ![NODE_TYPE.GROUP, NODE_TYPE.END].includes(
              item.graphNode.data.componentType
            )
        );

        // 如果有非GROUP/END类型的停止节点，则停止轮询
        if (otherNodes.length > 0) {
          // 停止轮询
          stopPolling();

          // 处理第一个非GROUP/END类型的节点
          const nodeToProcess = otherNodes[0];
          currentStoppedNode.value = nodeToProcess.dagNode;

          // 如果节点有页面结构，显示参数输入弹窗
          showPageModal(nodeToProcess.graphNode.data);
        } else if (groupOrEndNodes.length > 0 && runningNodes.length === 0) {
          // 如果只有GROUP/END类型的节点且没有运行中的节点，处理第一个GROUP/END节点
          stopPolling();

          const nodeToProcess = groupOrEndNodes[0];
          currentStoppedNode.value = nodeToProcess.dagNode;

          // 执行下一步调试
          nextStepDebug({
            wfInstanceId: singleStepDebugInstanceId.value,
            appId: APP_ID,
            params: {},
          }).then(() => {
            // 更新节点状态
            nodeToProcess.graphNode.setData({
              ...nodeToProcess.graphNode.data,
              status: NODE_STATUS.SUCCESS,
            });
            // 延迟500ms后继续获取运行信息
            setTimeout(() => {
              singleGetRunningInfo(params);
            }, 1000);
          });
        }
      })
      .catch(error => {
        console.error('获取单步调试信息失败:', error);
        stopPolling();
        message.error(error.message);
      });
  };

  /**
   * 停止轮询
   */
  const stopPolling = (): void => {
    if (workflowPollingId.value !== 0) {
      clearInterval(workflowPollingId.value);
      workflowPollingId.value = 0;
    }
  };

  // 立即执行一次
  pollRunningInfo();

  // 设置每4秒执行一次的轮询
  workflowPollingId.value = window.setInterval(pollRunningInfo, 4000);
};

/**
 * 显示参数输入弹窗
 * @param {any[]} schema - 页面结构数据
 * @param {string} nodeName - 节点名称
 */
const showPageModal = (nodeData: Tool): void => {
  const parsedSchema = JSON.parse(nodeData.pageStructure || '[]') as any[];
  if (!parsedSchema.length) return;
  showDialog(
    <ParametersInputModal pageSchema={parsedSchema} nodeData={nodeData} />
  ).then((params: any) => {
    // 获取当前实例ID，优先使用当前停止节点的实例ID
    const currentInstanceId =
      currentStoppedNode.value?.workflowInstanceId ||
      singleStepDebugInstanceId.value;
    // 执行下一步调试
    nextStepDebug({
      wfInstanceId: currentInstanceId,
      appId: APP_ID,
      params,
    }).then(() => {
      // 延迟500ms后继续获取运行信息
      setTimeout(() => {
        singleGetRunningInfo({
          wfInstanceId: singleStepDebugInstanceId.value,
          appId: APP_ID,
        });
      }, 1000);
    });
  });
};

/**
 * 自动运行工作流
 * 清除当前状态并启动工作流自动运行
 */
const handleRun = async (): Promise<void> => {
  try {
    // 清除工作流状态
    workflow.value?.clearStatus();

    // 运行工作流并获取实例ID
    const wfInstanceId = await runWorkflow(workflowParams.value.id!);

    // 开始轮询获取运行信息
    intervalGetRunningInfo({ wfInstanceId, appId: APP_ID });
  } catch (error: any) {
    console.error('运行工作流失败:', error);
    message.error(error.message);
  }
};

/**
 * 格式化工作流参数
 * @param {Node[]} nodes - 节点列表
 * @param {Edge[]} edges - 边列表
 * @param {boolean} isSubFlow - 是否为子流程
 * @param {string} subFlowId - 子流程ID
 * @returns 格式化后的工作流参数
 */
const formatWorkflowParams = (
  nodes: Node[],
  edges: Edge[],
  isSubFlow?: boolean,
  subFlowId?: string
) => {
  // 创建基础参数
  const formattedParams = {
    ...workflowParams.value,
    id: undefined,
    powerjobWorkflowId: workflowParams.value.id,
    wfName: workflowParams.value.wfName,
  };

  // 构建DAG结构
  formattedParams.dag = {
    // 格式化节点数据
    nodes: nodes.map(node => {
      const data = node.getData() as WorkflowNodeData;
      return {
        subWorkflowId: data.subWorkflowId,
        componentId: data.sysDesComponentId,
        pageNodeId: node.id,
        nodeType: data.nodeType,
        jobId: data.jobId,
        nodeName: data.componentName,
        enable: true,
        disableByControlNode: false,
        skipWhenFailed: false,
        cycleArray: data.cycleArray
          ? JSON.stringify(data.cycleArray)
          : undefined,
      };
    }),

    // 格式化边数据
    edges: edges.map(edge => {
      const fromNode = edge.getSourceCell()!;
      const fromPortId = edge.getSourcePortId();
      const to = edge.getTargetCellId()!;

      // 处理条件节点
      if (fromNode?.isNode() && fromNode.shape === GRAPH_SHAPE.CONDITION) {
        const { conditions } = fromNode.data as ConditionNodeData;
        return {
          from: fromNode.id,
          to,
          expression: conditions.find(condition => condition.id === fromPortId)
            ?.condition,
        };
      }

      // 处理普通节点
      return { from: fromNode.id, to };
    }),
  };

  // 处理子流程特殊情况
  if (isSubFlow) {
    formattedParams.powerjobWorkflowId = subFlowId;
    formattedParams.wfName = `${workflowParams.value.wfName}-子流程`;
  }

  // 序列化数据
  formattedParams.globalData = JSON.stringify(workflowParams.value.globalData);
  formattedParams.globalDataMap = JSON.stringify(
    workflowParams.value.globalDataMap
  );
  formattedParams.nodeDataMap = JSON.stringify(
    workflowParams.value.nodeDataMap
  );

  return formattedParams;
};

/**
 * 保存工作流
 * 保存当前工作流及其子流程
 */
const handleSave = async (): Promise<void> => {
  try {
    // 检查工作流是否存在
    if (!workflow.value) return;

    // 设置保存状态
    isSaving.value = true;

    // 获取所有节点和边
    const allNodes = workflow.value.getNodes();
    const edges = workflow.value.getEdges().filter(edge => !edge.parent);
    const topNodes = allNodes.filter(node => !node.parent);

    // 获取所有组节点（子流程）
    const groupNodes = allNodes.filter(
      node => node.shape === GRAPH_SHAPE.GROUP
    );

    // 先保存所有子流程
    for (const groupNode of groupNodes) {
      if (!groupNode.children) continue;

      // 获取子流程中的节点和边
      const childNodes = groupNode.children?.filter(
        child => child.shape === GRAPH_SHAPE.NORMAL
      ) as Node[];
      const childEdges = groupNode.children?.filter(
        child => child.shape === 'dashed-edge'
      ) as Edge[];

      // 获取节点数据并格式化参数
      const data = groupNode.getData();
      const params = formatWorkflowParams(
        childNodes,
        childEdges,
        true,
        data.subWorkflowId
      );

      // 为子流程也保存pageStructure
      // 由于子流程是嵌入在父节点中的，我们可以直接从groupNode获取子流程的完整结构
      // 创建一个包含子节点和边的对象，模拟toJSON的输出格式
      const subGraphData = {
        cells: groupNode.children ? [...groupNode.children] : [],
      };
      params.pageStructure = JSON.stringify(subGraphData);

      // 保存子流程并更新子流程ID
      const childProcess = await saveOrUpdate(params);
      groupNode.setData({ ...data, subWorkflowId: childProcess.id });
    }

    // 保存主流程
    const topParams = formatWorkflowParams(topNodes, edges);
    topParams.pageStructure = JSON.stringify(workflow.value.toJSON());

    // 保存工作流并获取结果
    const res = await saveOrUpdate(topParams);
    workflowParams.value.id = res.id;

    // 显示成功消息
    message.success('保存成功！');

    // 更新路由参数
    router.replace({
      ...route,
      query: { sysDesWorkflowId: res.sysDesWorkflowId },
    });
  } catch (error: any) {
    console.error('保存工作流失败:', error);
    message.error(error.message);
  } finally {
    // 无论成功失败，都重置保存状态
    isSaving.value = false;
  }
};

/**
 * 单步调试工作流
 * 清除当前状态并启动工作流单步调试
 */
const handleSingleStepDebug = async (): Promise<void> => {
  try {
    // 清除工作流状态
    workflow.value?.clearStatus();

    // 启动单步调试并获取实例ID
    const wfInstanceId = await startSingleStepDebug({
      workflowId: workflowParams.value.id!,
      appId: APP_ID,
    });

    // 保存调试实例ID
    singleStepDebugInstanceId.value = wfInstanceId;

    // 延迟500ms后开始轮询获取运行信息
    setTimeout(() => {
      singleGetRunningInfo({ wfInstanceId, appId: APP_ID });
    }, 1000);
  } catch (error: any) {
    console.error('启动单步调试失败:', error);
    message.error(error.message);
  }
};
</script>

<style lang="less">
.workflow-container {
  background: #f3f3f3;
  height: calc(100vh - 60px);
  padding: 10px 0;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: minmax(310px, 15%) 1fr 350px;
  overflow: hidden;
}

.header {
  height: 60px;
  background-color: white;
}
</style>
