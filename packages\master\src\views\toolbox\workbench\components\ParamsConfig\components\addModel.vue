<template>
  <a-modal
    v-model:open="dialog.visible"
    title="新增参数"
    width="400px"
    @cancel="dialog.cancel"
    @ok="handleOk"
  >
    <FormContent ref="formRef" />
  </a-modal>
</template>
<script setup lang="ts">
import { useDialog } from 'dialog-async';
import FormContent from './form-content.vue';

const dialog = useDialog();
const formRef = useTemplateRef('formRef');

const handleOk = async () => {
  const data = await formRef.value?.validate();
  dialog.submit(data);
};
</script>
