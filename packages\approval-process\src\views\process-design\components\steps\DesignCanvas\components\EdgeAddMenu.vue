<template>
  <div
    v-if="visible"
    class="edge-add-menu"
    :style="{ left: `${position.x}px`, top: `${position.y}px` }"
    @click.stop
  >
    <div class="menu-item" @click="e => handleAddNode('approval', e)">
      <UserOutlined />
      <span>审批人</span>
    </div>
    <div class="menu-item" @click="e => handleAddNode('cc', e)">
      <MailOutlined />
      <span>抄送人</span>
    </div>
    <div class="menu-item" @click="e => handleAddNode('handler', e)">
      <ToolOutlined />
      <span>办理人</span>
    </div>
    <div class="menu-item condition-item" @click="e => handleAddCondition(e)">
      <BranchesOutlined />
      <span>条件分支</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import UserOutlined from '~icons/ant-design/user-outlined';
import MailOutlined from '~icons/ant-design/mail-outlined';
import ToolOutlined from '~icons/ant-design/tool-outlined';
import BranchesOutlined from '~icons/ant-design/branches-outlined';

defineProps<{
  visible: boolean;
  position: { x: number; y: number };
  edge: any;
}>();

const emit = defineEmits(['add-node', 'add-condition', 'close']);

// 添加节点
const handleAddNode = (type: string, e: MouseEvent) => {
  // 阻止事件冒泡，确保事件不会传播到document
  e.stopPropagation();
  emit('add-node', type);
};

// 添加条件分支
const handleAddCondition = (e: MouseEvent) => {
  // 阻止事件冒泡，确保事件不会传播到document
  e.stopPropagation();
  emit('add-condition');
};
</script>

<style scoped>
.edge-add-menu {
  position: fixed;
  width: 120px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transform: translate(-50%, 10px);
}

.menu-item {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: #f5f5f5;
  color: #40cfa0;
}

.menu-item span {
  margin-left: 8px;
}

.condition-item {
  border-top: 1px solid #f0f0f0;
}
</style>
