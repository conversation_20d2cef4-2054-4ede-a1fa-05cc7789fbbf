/**
 * @file registerEdgeTools.ts
 * @description 注册审批流程边工具
 */

import { Graph } from '@antv/x6';

/**
 * 注册审批流程边工具
 */
export function registerApprovalEdgeTools(): void {
  // 注册加号按钮工具
  Graph.registerEdgeTool(
    'edge-add-button',
    {
      inherit: 'button',
      markup: [
        {
          tagName: 'circle',
          selector: 'button',
          attrs: {
            r: 12,
            stroke: '#40CFA0',
            'stroke-width': 1,
            fill: 'white',
            cursor: 'pointer',
          },
        },
        {
          tagName: 'path',
          selector: 'icon',
          attrs: {
            d: 'M-6 0 L6 0 M0 -6 L0 6',
            stroke: '#40CFA0',
            'stroke-width': 1.5,
            fill: 'none',
          },
        },
      ],
      useCellGeometry: true,
      offset: 0,
      distance: 30,
      onClick: ({ e, view }: any) => {
        // 阻止事件冒泡，防止触发document的点击事件
        e.stopPropagation();

        const graph = view.graph;
        const edge = view.cell;

        if (graph && typeof graph.toggleEdgeAddMenu === 'function') {
          graph.toggleEdgeAddMenu(e, edge);
        }
      },
    },
    true
  );
}
