import * as echarts from 'echarts/core';
import {
  TitleComponent,
  LegendComponent,
  TooltipComponent,
  GridComponent,
  ToolboxComponent,
  GraphicComponent,
  VisualMapComponent,
  DatasetComponent,
  BrushComponent,
} from 'echarts/components';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Heatmap<PERSON>hart,
} from 'echarts/charts';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer, SVGRenderer } from 'echarts/renderers';
import { type ChartType } from './type';
import {
  type ShallowRef,
  shallowRef,
  type Ref,
  useAttrs,
  onBeforeUnmount,
  shallowReactive,
} from 'vue';
import themeObj from './westeros.json';

// 导入options模块

interface ChartHookOption {
  type?: Ref<ChartType>;
  el: ShallowRef<HTMLElement>;
  theme: Ref<string>;
  options: Ref<echarts.EChartsCoreOption>;
  renderer: Ref<'svg' | 'canvas'>;
}

class ChartsResize {
  #charts = new Set<echarts.ECharts>();
  #timeId: any = undefined;
  constructor() {}

  getCharts() {
    return [...this.#charts];
  }

  handleResize() {
    clearTimeout(this.#timeId);
    this.#timeId = setTimeout(() => {
      this.#charts.forEach(chart => {
        chart.resize();
      });
    }, 300);
  }

  add(chart: echarts.ECharts) {
    this.#charts.add(chart);
  }

  remove(chart: echarts.ECharts) {
    this.#charts.delete(chart);
  }

  removeListener() {
    window.removeEventListener('resize', this.handleResize);
  }
}

const chartResize = new ChartsResize();

export const observer = new ResizeObserver(() => {
  chartResize.handleResize();
});

export const useCharts = ({
  el,
  theme,
  options,
  renderer,
}: ChartHookOption) => {
  echarts.use([
    BarChart,
    LineChart,
    PieChart,
    ScatterChart,
    HeatmapChart,
    TitleComponent,
    LegendComponent,
    TooltipComponent,
    GridComponent,
    LabelLayout,
    BrushComponent,
    ToolboxComponent,
    CanvasRenderer,
    SVGRenderer,
    UniversalTransition,
    GraphicComponent,
    VisualMapComponent,
    DatasetComponent,
  ]);
  echarts.registerTheme('westeros', themeObj);
  const charts = shallowRef<echarts.ECharts>();
  // 获取属性
  const attrs = useAttrs();

  // 初始化事件
  const initEvent = () => {
    Object.keys(attrs).forEach(attrKey => {
      if (attrKey.startsWith('on')) {
        const cb = attrs[attrKey];
        // 移除on-chart前缀，并将其转换为小写
        attrKey = attrKey.replace(/^on(-chart-)?/, '');
        let [eventName, modifier] = attrKey.split(':');
        eventName = eventName
          .split('-')
          .map((k, i) => (i > 0 ? `${k[0].toUpperCase()}${k.substring(1)}` : k))
          .join('');
        // 将回调函数注册到charts中
        if (typeof cb === 'function') {
          if (modifier) {
            charts.value?.on(eventName, modifier, cb as () => void);
          } else {
            charts.value?.on(eventName, cb as () => void);
          }
        }
      }
    });
  };

  // 设置echarts的配置项
  const setOptions = (opt: echarts.EChartsCoreOption) => {
    // 重新计算图表大小
    charts.value?.resize();
    // 设置图表配置项
    charts.value?.setOption(opt, true);
  };

  const changeTheme = (themeName: string) => {
    const oldChartsInstance = charts.value;
    if (!oldChartsInstance) return;
    charts.value?.dispose();
    chartResize.remove(oldChartsInstance);
    charts.value = echarts.init(el.value, themeName);
    charts.value?.setOption(options.value);
  };

  // 初始化图表
  const initChart = async () => {
    charts.value?.dispose();
    // 初始化echarts实例
    charts.value = echarts.init(el.value, theme.value, {
      renderer: renderer.value,
    });
    observer.observe(el.value);
    // 设置图表配置项
    // charts.value.setOption(options || {})
    // 监听图表大小变化
    chartResize.add(charts.value);
    // 初始化事件
    initEvent();
  };

  watch(
    () => theme.value,
    value => {
      changeTheme(value);
    }
  );
  // 销毁前
  onBeforeUnmount(() => {
    // 移除图表大小变化监听
    chartResize.remove(charts.value!);
    observer.unobserve(el.value);
  });

  return {
    charts,
    setOptions,
    initChart,
    initEvent,
  };
};

export const chartsOptions = <T extends echarts.EChartsCoreOption>(option: T) =>
  shallowReactive<T>(option);

export * from './type.d';
