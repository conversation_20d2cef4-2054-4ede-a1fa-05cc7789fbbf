/**
 * @file useProcessContext.ts
 * @description 流程设计上下文钩子，用于共享流程设计状态
 */

import { createContextHook } from '@kd/hooks';
import type { ProcessDesignData } from '../../types/process';

/**
 * 创建流程设计上下文钩子
 */
export const { useProcessContext, createProcessContext } = createContextHook(
  'ProcessContext',
  () => {
    /**
     * 流程设计数据
     */
    const processData = ref<ProcessDesignData>({
      icon: {
        type: 'text',
        content: '文字',
        backgroundColor: '#ff4d4f',
        color: '#ffffff',
      },
      name: '',
      description: '',
      group: '',
      form: {
        structure: [],
        data: {},
      },
      flow: {
        nodes: [],
        edges: [],
      },
      settings: {},
    });

    /**
     * 保存流程数据
     */
    const saveProcess = async () => {
      try {
        // 更新最近保存时间
        processData.value.lastSavedTime = new Date().toLocaleString();
        // TODO: 调用API保存流程数据
        console.log('保存流程数据', processData.value);
        return true;
      } catch (error) {
        console.error('保存流程数据失败', error);
        return false;
      }
    };

    /**
     * 发布流程
     */
    const publishProcess = async () => {
      try {
        // TODO: 调用API发布流程
        console.log('发布流程', processData.value);
        return true;
      } catch (error) {
        console.error('发布流程失败', error);
        return false;
      }
    };

    return {
      processData,
      saveProcess,
      publishProcess,
    };
  }
);
