import { ProcessStatus } from '../../types/process';

/**
 * 获取流程状态文本
 * @param status 状态
 * @returns 状态文本
 */
export const getStatusText = (status: ProcessStatus): string => {
  const statusMap: Record<ProcessStatus, string> = {
    [ProcessStatus.DRAFT]: '草稿',
    [ProcessStatus.PUBLISHED]: '已发布',
    [ProcessStatus.DISABLED]: '已禁用',
  };
  return statusMap[status] || '未知状态';
};

/**
 * 获取流程状态颜色
 * @param status 状态
 * @returns 状态颜色
 */
export const getStatusColor = (status: ProcessStatus): string => {
  const colorMap: Record<ProcessStatus, string> = {
    [ProcessStatus.DRAFT]: 'blue',
    [ProcessStatus.PUBLISHED]: 'green',
    [ProcessStatus.DISABLED]: 'red',
  };
  return colorMap[status] || 'default';
};

/**
 * 获取状态选项
 * @returns 状态选项数组
 */
export const getStatusOptions = () => [
  { label: '全部', value: '' },
  { label: '草稿', value: ProcessStatus.DRAFT },
  { label: '已发布', value: ProcessStatus.PUBLISHED },
  { label: '已禁用', value: ProcessStatus.DISABLED },
];
