<template>
  <div
    class="h-[60px] flex items-center"
    style="border-bottom: 1px solid #e8e8ea"
  >
    <!-- Left section: Process name and back icon -->
    <div class="flex items-center px-4 w-1/4">
      <a-button
        type="link"
        class="flex items-center p-0 mr-2"
        @click="handleBack"
      >
        <template #icon>
          <LeftOutlined />
        </template>
      </a-button>
      <span class="text-lg font-medium">{{ processName || '未命名审批' }}</span>
      <span v-if="lastSavedTime" class="ml-4 text-gray-400 text-sm">
        最近保存: {{ lastSavedTime }}
      </span>
    </div>

    <!-- Center section: Step tabs -->
    <div class="flex-1 flex justify-center">
      <a-steps
        :current="currentStep"
        @change="handleStepChange"
        class="w-[600px]"
        size="small"
      >
        <a-step title="基础信息" />
        <a-step title="表单设计" />
        <a-step title="流程设计" />
        <a-step title="更多设置" />
      </a-steps>
    </div>

    <!-- Right section: Preview and Publish buttons -->
    <div class="flex items-center gap-2 px-4 w-1/4 justify-end">
      <a-button @click="handlePreview">预览</a-button>
      <a-button type="primary" @click="handlePublish">发布</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import LeftOutlined from '~icons/ant-design/left-outlined';

defineProps<{
  processName?: string;
  lastSavedTime?: string;
  currentStep: number;
}>();

const emit = defineEmits<{
  (e: 'preview'): void;
  (e: 'publish'): void;
  (e: 'update:currentStep', step: number): void;
}>();

const router = useRouter();

const handleBack = () => {
  router.push('/process-manage');
};

const handlePreview = () => {
  emit('preview');
};

const handlePublish = () => {
  emit('publish');
};

const handleStepChange = (step: number) => {
  emit('update:currentStep', step);
};
</script>
