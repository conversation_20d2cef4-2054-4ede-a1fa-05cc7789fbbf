import { ref } from 'vue';
import type { DebugLog, DebugBinding } from './types';

// 创建一个闭包来保存单例实例
let instance: ReturnType<typeof createDebugLogInstance> | null = null;

// 创建实际的调试日志实例
function createDebugLogInstance() {
  const debugLogs = ref<DebugLog[]>([]);

  function addLog(message: string): void {
    debugLogs.value.push({
      time: new Date().toLocaleTimeString(),
      log: message,
    });
  }

  function logDebugStart(bindings: DebugBinding[]): void {
    addLog('开始调试...');

    const inputLogs = bindings.map(
      binding => `${binding.props.label}:${binding.props.value}`
    );
    addLog(`输入参数: ${inputLogs.join('；')}`);
  }

  function logSoftwareRunning(): void {
    addLog('软件运行中，请稍候...');
  }

  function logDebugComplete(): void {
    addLog('调试结束，输出结果已返回');
  }

  function clearLogs(): void {
    debugLogs.value = [];
  }

  return {
    debugLogs,
    addLog,
    logDebugStart,
    logSoftwareRunning,
    logDebugComplete,
    clearLogs,
  };
}

// 导出单例钩子函数
export function useDebugLogSingleton() {
  // 如果实例不存在，则创建一个
  if (!instance) {
    instance = createDebugLogInstance();
  }
  
  // 返回单例实例
  return instance;
}
