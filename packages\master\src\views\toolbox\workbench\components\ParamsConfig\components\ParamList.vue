<template>
  <div>
    <div class="font-bold text-[#666]">
      <slot name="icon"></slot>
      <span>{{ title }}</span>
      <a-button type="link" class="float-right" @click="handleRemoveAll">
        全部清空
      </a-button>
    </div>
    <ul class="p-2">
      <li
        v-for="param in params"
        :key="param.key"
        class="flex w-full items-center cursor-pointer py-[2px]"
      >
        <a-popover :title="param.commandCode">
          <span
            class="mr-auto hover:bg-[#f5f5f5] overflow-hidden text-ellipsis whitespace-nowrap"
            :class="{ 'is-selected': selectedParamKey === param.key }"
            @click="handleClick(param)"
          >
            {{ param.commandCode }}
          </span>
        </a-popover>
        <a-popconfirm
          title="确定删除当前参数"
          ok-text="确定"
          cancel-text="取消"
          @confirm="handleRemove(param.key)"
        >
          <AntDesignDeleteOutlined class="text-red-400" />
        </a-popconfirm>
      </li>
    </ul>
    <a-empty v-if="params.length === 0" />
  </div>
</template>

<script setup lang="ts">
import { Params } from '@/master/types/tool';
import AntDesignDeleteOutlined from '~icons/ant-design/delete-outlined';

defineProps<{ title: string; params: Params[] }>();

const emit = defineEmits(['remove', 'select', 'removeAll']);

const selectedParamKey = ref<string>();

const handleRemove = (key: string) => {
  emit('remove', key);
};

const handleRemoveAll = () => {
  emit('removeAll');
};

const handleClick = (param: Params) => {
  selectedParamKey.value = param.key;
  emit('select', param);
};
</script>

<style>
.is-selected {
  background-color: #f5f5f5;
}
</style>
