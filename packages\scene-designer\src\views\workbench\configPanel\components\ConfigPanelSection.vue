<template>
  <a-collapse-panel v-if="!isEmpty(model)">
    <template #header>
      <component :is="icon" />
      <span class="ml-1">{{ title }}</span>
    </template>
    <config-form-item
      v-for="(item, key) in model"
      :key="key"
      :item="item"
      :item-key="key"
      :on-change="onChange"
    />
  </a-collapse-panel>
</template>

<script setup lang="ts">
import { isEmpty } from '../utils';
import ConfigFormItem from './ConfigFormItem.vue';

/**
 * Configuration item interface
 */
interface ConfigItem {
  label: string;
  comp: string | object;
  value: any;
  compProps?: Record<string, any>;
}

/**
 * Props for ConfigPanelSection component
 */
interface Props {
  /** Section title */
  title: string;
  /** Icon component */
  icon: string | object;
  /** Configuration model containing items */
  model: Record<string, ConfigItem>;
  /** Change handler callback */
  onChange: (params: {
    key: string;
    value: any;
    option?: Global.Option;
    isInjectProps?: boolean;
  }) => void;
}

defineProps<Props>();
</script>

<style scoped>
.ml-1 {
  margin-left: 4px;
}

.empty-message {
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 16px 0;
}
</style>
