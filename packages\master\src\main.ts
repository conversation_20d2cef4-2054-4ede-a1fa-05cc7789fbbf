import dayjs from 'dayjs';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import 'dayjs/locale/zh-cn';
import '@/master/styles/index.css';
import directives from 'shared/directives';
import { createPinia } from 'pinia';
import { storage } from '@kd/utils';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

dayjs.locale('zh-cn');

const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

const jwt = new URLSearchParams(location.search).get('jwt');

if (jwt) {
  storage.set('authorization', jwt);
}

app.use(pinia).use(router).use(directives);
app.mount('#app');
