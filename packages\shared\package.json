{"name": "shared", "private": "true", "version": "1.0.0", "description": "Shared code in packages scope", "main": "index.ts", "module": "index.ts", "type": "module", "dependencies": {"vue-advanced-cropper": "^2.8.9", "radash": "catalog:", "ant-design-vue": "catalog:", "pinia": "catalog:", "dayjs": "catalog:", "@kd/utils": "catalog:", "vue-router": "catalog:", "vue": "catalog:", "echarts": "catalog:"}}