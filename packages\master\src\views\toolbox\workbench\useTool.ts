import type { ToolCreateParams } from '@/master/types/tool';
import { createContextHook } from '@kd/hooks';
import { ToolStatus, ToolType } from './constants';

export const { createToolContext, useToolContext } = createContextHook(
  'ToolContext',
  () => {
    const toolParams = ref<ToolCreateParams>({
      componentIcon: undefined,
      componentName: '',
      componentOrder: 0,
      componentStatus: ToolStatus.WAIT_APPROVAL,
      componentType: ToolType.SOFT,
      desProfessionalSoftwareId: '',
      enable: 1,
      commands: [],
      inputParams: [],
      outputParams: [],
      nodeType: 1,
      pageStructure: [],
      paramsMap: [],
      remarks: '',
      bsflag: 'N',
      sysDesModelId: '',
      timeoutDuration: 120,
      sysDesComponentId: '',
      sysDesComponentTypeId: '',
    });

    return { toolParams };
  }
);
