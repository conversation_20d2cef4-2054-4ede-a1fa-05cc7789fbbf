export default {
  placeholder: {
    label: '提示内容',
    value: '选择',
    comp: 'a-input',
    compProps: {
      allowClear: true,
    },
  },
  label: {
    label: '标题',
    value: '标题',
    comp: 'a-input',
    compProps: {
      allowClear: true,
    },
  },
  options: {
    label: '选项',
    value: undefined,
    comp: 'RenderSelect',
    compProps: {
      options: [{ value: '', label: '' }],
    },
  },
  // value: {
  //   label: '值',
  //   value: undefined,
  //   comp: 'a-input',
  //   compProps: {
  //     allowClear: true,
  //   },
  // },
} as Widget.PropsConfig;
