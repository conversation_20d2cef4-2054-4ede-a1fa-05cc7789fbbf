import { RadioGroup, RadioGroupProps } from 'ant-design-vue';
export default ({
  label,
  style,
  list,
  ...props
}: RadioGroupProps & {
  label: any;
  style?: Record<string, string>;
  list:
    | string[]
    | number[]
    | Array<{ label: string; value: string; disabled?: boolean }>;
}) => {
  return (
    <div
      style={{
        display: 'inline-flex',
        alignItems: 'center',
      }}
    >
      {label && (
        <label style="white-space: nowrap;padding:0 8px;">{label}:</label>
      )}
      <RadioGroup {...props} options={list} />
    </div>
  );
};
