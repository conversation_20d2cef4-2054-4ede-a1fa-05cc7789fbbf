export default {
  checked: {
    label: '是否选中',
    value: true,
    comp: 'a-radio-group',
    compProps: {
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
  },
  // size: {
  //   label: '尺寸',
  //   value: 'default',
  //   comp: 'a-select',
  //   compProps: {
  //     options: [
  //       { label: 'deafult', value: 'default' },
  //       { label: 'small', value: 'small' },
  //     ],
  //   },
  // },
  checkedChildren: {
    label: '选中时显示',
    value: '',
    comp: 'a-input',
  },
} as Widget.PropsConfig;
