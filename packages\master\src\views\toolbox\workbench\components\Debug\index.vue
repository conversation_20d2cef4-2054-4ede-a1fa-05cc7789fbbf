<template>
  <div class="h-full border border-[#f0f0f0] border-solid rounded relative">
    <Teleport to="#footer">
      <a-button type="primary" ghost @click="handleDebug">
        <template #icon>
          <CodiconDebugStart />
        </template>
        调试
      </a-button>
    </Teleport>
    <Preview ref="ScenePreviewRef" :schema="toolParams.pageStructure" />
    <DebugLogPanel
      ref="debugLogPanelRef"
      class="absolute left-0 bottom-0"
      @debug:complete="handleDebugComplete"
    />
  </div>
</template>

<script setup lang="tsx">
import { Preview } from 'scene-designer';
import CodiconDebugStart from '~icons/codicon/debug-start';
import { useToolContext } from '../../useTool';
import DebugLogPanel from './DebugLogPanel.vue';
import { message } from 'ant-design-vue';

const { toolParams } = useToolContext();

const scenePreviewRef = useTemplateRef('ScenePreviewRef');
const debugLogPanelRef = useTemplateRef('debugLogPanelRef');
const isDebugCompleted = ref(false);

const handleDebug = () => {
  if (debugLogPanelRef.value) {
    debugLogPanelRef.value.collapsed = false;
    const jsonObj = scenePreviewRef.value?.getBindingMap();
    debugLogPanelRef.value.startDebug(jsonObj || []);
  }
};

const handleDebugComplete = (result: string) => {
  isDebugCompleted.value = true;
  const resObj = JSON.parse(result);
  Object.keys(resObj).forEach(r => {
    scenePreviewRef.value?.setValue(
      r,
      Array.isArray(resObj[r]) ? resObj[r][0] : resObj[r]
    );
  });
};

const validate = async () => {
  // if (!isDebugCompleted.value) {
  //   message.error('组件调试成功后才可保存！');
  //   return false;
  // }
  return true;
};

defineExpose({
  validate,
});
</script>
