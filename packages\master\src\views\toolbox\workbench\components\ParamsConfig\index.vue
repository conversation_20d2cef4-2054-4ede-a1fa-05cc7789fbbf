<template>
  <div class="grid grid-cols-[400px_1fr] h-full gap-1">
    <Left />
    <Right />
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import { useToolContext } from '../../useTool';
import Left from './components/left.vue';
import Right from './components/right.vue';

const { toolParams } = useToolContext();

const validate = async () => {
  const { inputParams, outputParams } = toolParams.value;
  if (inputParams.length === 0 || outputParams.length === 0) {
    message.error('输入参数和输出参数不能为空');
    return false;
  }
  return true;
};

defineExpose({
  validate,
});
</script>
