/**
 * @file IconNode.tsx
 * @description 图标节点组件，用于在工作流图中显示节点图标和状态
 */

import { Graph, Node } from '@antv/x6';
import { getIcon } from '@/master/views/toolbox/icons';
import { NODE_STATUS } from '../../../constants';
import Reload from '~icons/ant-design/reload-outlined';
import Success from '~icons/ant-design/check-circle-outlined';
import Failed from '~icons/ant-design/close-circle-outlined';
import style from './iconNode.module.css';
import Icon from 'shared/components/Icon/index.vue';
/**
 * 图标节点组件
 */
export default defineComponent({
  name: 'IconNode',
  props: {
    /** 节点实例 */
    node: Object as PropType<Node>,
    /** 图形实例 */
    graph: Object as PropType<Graph>,
  },
  setup(props) {
    // 节点数据
    const data = ref(props.node?.getData() || {});
    // 节点是否被选中
    // 监听节点数据变化
    props.node!.on('change:data', ({ current }) => {
      data.value = current;
    });
    const parsedIcon = (icon: string) => {
      return JSON.parse(icon);
    };

    const isObjectString = computed(() =>
      data.value.componentIcon.startsWith('{')
    );
    return { data, parsedIcon, isObjectString };
  },

  render() {
    return (
      <div class={[style.iconNode, this.data.isSelected ? style.selected : '']}>
        {/* 显示节点图标 */}
        {this.data?.componentIcon && (
          <div class={style.iconNodeIcon}>
            {this.isObjectString ? (
              <Icon value={this.parsedIcon(this.data.componentIcon)} />
            ) : (
              h(getIcon(this.data.componentIcon) as any)
            )}
          </div>
        )}
        {/* 显示节点状态 */}
        <div class={style.status}>
          {/* 运行中状态 */}
          {this.data?.status === NODE_STATUS.RUNNING && (
            <Reload style="animation: spin 1s infinite linear" />
          )}
          {/* 成功状态 */}
          {this.data?.status === NODE_STATUS.SUCCESS && (
            <Success style={{ color: 'oklch(0.768 0.233 130.85)' }} />
          )}
          {/* 失败状态 */}
          {this.data?.status === NODE_STATUS.FAILED && (
            <Failed style={{ color: 'oklch(0.637 0.237 25.331)' }} />
          )}
        </div>
        <span class={style.nodeName}>{this.data?.componentName}</span>
      </div>
    );
  },
});
