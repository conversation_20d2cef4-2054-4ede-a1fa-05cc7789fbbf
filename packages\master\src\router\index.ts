import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import { eventEmitter } from '@kd/utils';
import { createRouterGuards } from './routerGuards';
import Page404 from 'shared/pages/404.vue';
import PageLayout from 'shared/layouts/index.vue';
import systemSetting from './systemSetting';
import AntDesignDashboardOutlined from '~icons/ant-design/dashboard-outlined';
import MdiTools from '~icons/mdi/tools';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: PageLayout,
    name: 'home',
    redirect: '/dashboard',
    meta: { isRoot: true },
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/master/views/dashboard/index.vue'),
        meta: {
          title: '工作台',
          icon: AntDesignDashboardOutlined,
        },
      },
      {
        path: 'toolbox',
        name: 'toolbox',
        component: () => import('@/master/views/toolbox/index.vue'),
        meta: {
          title: '工具箱',
          icon: MdiTools,
        },
      },
      {
        path: '/toolbox/workbench',
        name: 'toolbox-workbench',
        component: () => import('@/master/views/toolbox/workbench/index.vue'),
        meta: { showInMenu: false },
      },
      ...systemSetting,
    ],
  },

  {
    path: '/workbench',
    name: 'workbench',
    component: () => import('@/master/views/workbench/index.vue'),
    meta: { showInMenu: false },
  },
  {
    path: '/:pathMatch(.*)*',
    name: '404',
    component: Page404,
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

eventEmitter.on('API_UN_AUTH', async () => {
  await router.push('/login');
});

createRouterGuards(router);

export default router;
