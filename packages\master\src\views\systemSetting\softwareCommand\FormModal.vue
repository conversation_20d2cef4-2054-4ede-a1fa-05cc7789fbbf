<template>
  <a-modal
    v-model:open="dialog.visible"
    :title="isNew ? '新建' : '编辑'"
    width="40%"
    :destroy-on-close="true"
    @cancel="dialog.cancel"
  >
    <a-form
      ref="formRef"
      :model="info"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 12 }"
    >
      <a-form-item label="命令名称" name="commandName">
        <a-input v-model:value="info.commandName" />
      </a-form-item>
      <a-form-item label="命令编码" name="commandCode">
        <a-input v-model:value="info.commandCode" />
      </a-form-item>
      <a-form-item label="操作类型" name="operateType">
        <a-input v-model:value="info.operateType" />
      </a-form-item>
      <!-- <a-form-item label="创建人员" name="createUser">
        <a-input v-model:value="info.createUser" :disabled="true" />
      </a-form-item>
      <a-form-item label="创建时间" name="createTime">
        <a-input v-model:value="info.createTime" :disabled="true" />
      </a-form-item>
      <a-form-item v-if="!isNew" label="更新人员" name="updateUser">
        <a-input v-model:value="info.updateUser" :disabled="true" />
      </a-form-item>
      <a-form-item v-if="!isNew" label="更新时间" name="updateTime">
        <a-input v-model:value="info.updateTime" :disabled="true" />
      </a-form-item> -->
    </a-form>

    <template #footer>
      <div>
        <a-button @click="dialog.cancel">取消</a-button>
        <a-button type="primary" @click="handldSubmit">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { addCommand, updateCommand } from '@/master/apis/commands';
import { message } from 'ant-design-vue';
import { Rule } from 'ant-design-vue/es/form';
import dayjs from 'dayjs';
import { useDialog } from 'dialog-async';
import { debounce, clone } from 'radash';

const { isNew, modelId, command, getList } = defineProps<{
  isNew: boolean;
  modelId: string;
  command?: any;
  getList: () => void;
}>();

const dialog = useDialog();

const formRef = ref();

const info = ref({
  desProfessionalSoftwareId: modelId,
  softwareCode: 'pipesim',
  commandName: '',
  commandCode: '',
  operateType: '',
  objectType: '',
  parameter_path: '',
  english_name: '',
  chinese_name: '',
  createUser: '1',
  createTime: isNew ? dayjs().format('YYYY-MM-DD HH:mm:ss') : '',
  updateUser: '1',
  updateTime: isNew ? '' : dayjs().format('YYYY-MM-DD HH:mm:ss'),
  // unit: '',
  // valueEnum: '',
});

const rules: Record<string, Rule[]> = {
  commandName: [
    { required: true, message: '请输入命令名称', trigger: 'change' },
  ],
  commandCode: [
    { required: true, message: '请输入命令编码', trigger: 'change' },
  ],
  operateType: [
    { required: true, message: '请输入操作类型', trigger: 'change' },
  ],
};

const handldSubmit = debounce({ delay: 200 }, () => {
  formRef.value
    .validate()
    .then(async () => {
      if (isNew) {
        const res = await addCommand(info.value);
        if (res) {
          message.success('新建成功');
        }
      } else {
        const res = await updateCommand(info.value);
        if (res) {
          message.success('修改成功');
        }
      }
      dialog.cancel();
      getList();
    })
    .catch((error: any) => {
      console.log('error', error);
    });
});

onMounted(() => {
  if (command) info.value = clone(command);
});
</script>
<style scoped lang="less"></style>
