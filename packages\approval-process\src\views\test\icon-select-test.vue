<template>
  <div class="p-6">
    <h1 class="text-xl font-bold mb-4">IconSelect 组件测试</h1>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-2">基本用法</h2>
      <div class="flex items-center gap-4">
        <IconSelect v-model="icon" @change="handleIconChange" />
        <div class="ml-4">
          <pre class="bg-gray-100 p-2 rounded">{{ JSON.stringify(icon, null, 2) }}</pre>
        </div>
      </div>
    </div>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-2">预设文字图标</h2>
      <div class="flex items-center gap-4">
        <IconSelect v-model="textIcon" @change="handleIconChange" />
        <div class="ml-4">
          <pre class="bg-gray-100 p-2 rounded">{{ JSON.stringify(textIcon, null, 2) }}</pre>
        </div>
      </div>
    </div>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-2">预设系统图标</h2>
      <div class="flex items-center gap-4">
        <IconSelect v-model="svgIcon" @change="handleIconChange" />
        <div class="ml-4">
          <pre class="bg-gray-100 p-2 rounded">{{ JSON.stringify(svgIcon, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import IconSelect from '../../components/IconSelect/index.vue';
import type { ProcessIcon } from '../../types/process';

// 默认为空
const icon = ref<ProcessIcon | null>(null);

// 预设文字图标
const textIcon = ref<ProcessIcon>({
  type: 'text',
  content: 'A',
  backgroundColor: '#1890ff',
  color: '#ffffff',
});

// 预设系统图标
const svgIcon = ref<ProcessIcon>({
  type: 'svg',
  content: 'user',
});

// 图标变更处理函数
const handleIconChange = (newIcon: ProcessIcon) => {
  console.log('Icon changed:', newIcon);
};
</script>
