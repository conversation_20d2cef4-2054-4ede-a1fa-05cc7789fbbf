<template>
  <template v-if="schema.children && schema.children.length > 0">
    <Wrapper :schema="schema">
      <component
        :is="schema.comp"
        :id="schema.id"
        v-bind="schema.props"
        :style="schema.style"
        @click.stop="handleCompClick(schema.id)"
      >
        <RenderComponent
          v-for="child in schema.children"
          :key="child.id"
          :schema="child"
        />
      </component>
    </Wrapper>
  </template>
  <Wrapper v-else :schema="schema">
    <component
      :is="schema.comp"
      :id="schema.id"
      v-bind="schema.props"
      :style="schema.style"
      @click.stop="handleCompClick(schema.id)"
    />
  </Wrapper>
  <!-- @click.stop="handleCompClick(schema.id)" -->
</template>
<script lang="ts" setup>
import useSchemaStore from '@/scene-designer/stores/schema';
import Wrapper from './Wrapper.vue';

const { schema } = defineProps<{
  schema: Widget.RenderConfig;
}>();

const schemaStore = useSchemaStore();

const handleCompClick = (id: string) => {
  schemaStore.setCurrentId(id);
};
</script>
