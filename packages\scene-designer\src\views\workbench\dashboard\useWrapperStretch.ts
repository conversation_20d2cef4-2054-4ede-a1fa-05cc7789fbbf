import useSchemaStore from "../../../stores/schema";

export default () => {
  const schema = useSchemaStore();
  const el = ref<HTMLElement>();
  const isStretch = ref(false);
  const startPoint = ref([0, 0]);
  const stretchDirection = ref("");
  /**
   * 处理拉伸开始的函数
   * @param e MouseEvent - 触发事件的鼠标事件对象
   * @param direction string - 拉伸的方向，如left, right, top, bottom
   *
   * 此函数用于初始化拉伸过程。
   * 它首先记录拉伸的起始点（鼠标位置），设置拉伸状态为true，
   * 并记录拉伸的方向。
   * 然后，它向window对象添加mousemove事件监听器以处理拉伸过程，
   * 并向window对象添加mouseup事件监听器以在拉伸结束时执行清理工作。
   * mouseup事件监听器设置为只触发一次，以确保在拉伸结束时只执行一次清理工作。
   */
  const handleStretchStart = (e: MouseEvent, direction: string) => {
    startPoint.value = [e.clientX, e.clientY];
    isStretch.value = true;
    stretchDirection.value = direction;
    window.addEventListener("mousemove", handleStretch);
    window.addEventListener(
      "mouseup",
      (e) => {
        handleStretchEnd(e);
        window.removeEventListener("mousemove", handleStretch);
      },
      { once: true }
    );
  };

  /**
   * 处理组件拉伸的逻辑
   *
   * @param e MouseEvent - 鼠标事件对象
   * @description 根据鼠标位置调整组件的宽度，同时保证组件不会超出画板边界，且不会小于设定的最小宽度
   */
  const handleStretch = (e: MouseEvent) => {
    const { location } = schema.getSchemaById(schema.currentId);
    let distance = e.clientX - startPoint.value[0];
    const { boardAttr } = editor.store;
    // 判断是否超出边界
    if (e.clientX > boardAttr.left + boardAttr.width) {
      distance = boardAttr.width - location.x - location.w - 24;
    }
    if (e.clientX < boardAttr.left) {
      distance = startPoint.value[0] - boardAttr.left;
    }
    let width = location.w + distance;
    // 判断是否小于最小宽度
    if (width < location.minW) {
      width = location.minW;
    }
    requestAnimationFrame(() => {
      if (!el.value) return;
      el.value.style.width = `${width}px`;
    });
  };

  /**
   * 处理拉伸结束的事件
   *
   * @param e MouseEvent - 鼠标事件对象
   *
   * 当用户结束对某个元素的拉伸时，此函数会被调用。它会将isStretch状态设置为false，
   * 读取被拉伸元素的left, top, width, height样式值，
   * 然后调用editor.actions.setCurrentComPosition方法更新当前组件的位置和大小。
   */
  const handleStretchEnd = (e: MouseEvent) => {
    isStretch.value = false;
    const { left, top, width, height } = el.value!.style;
    // editor.actions.setCurrentComPosition({
    //   x: left,
    //   y: top,
    //   w: width,
    //   h: height,
    // });
  };

  return {
    el,
    isStretch,
    handleStretchStart,
  };
};
