import KCard from '../KCard';
import KInput from '../KInput';
import useWorkbenchStore from '../../stores/workbench';

export default defineComponent({
  name: 'KBinder',
  setup() {
    const workbenchStore = useWorkbenchStore();
    const inputParams = workbenchStore.injectSource.filter(
      t => t.type === 'in'
    );
    const outputParams = workbenchStore.injectSource.filter(
      t => t.type === 'out'
    );
    return () => (
      <div>
        <KCard title="输入参数">
          <div>
            {inputParams.map(item => (
              <KInput
                label={item.label || item.commandCode}
                unit={item.unit}
                value={item.value}
                binding={item.label}
              />
            )) || null}
          </div>
        </KCard>
        <KCard title="输出参数" style="margin-top: 10px">
          <div>
            {outputParams.map(item => (
              <KInput
                label={item.label || item.commandCode}
                unit={item.unit}
                value={item.value}
                binding={item.label}
              />
            )) || null}
          </div>
        </KCard>
      </div>
    );
  },
});
