import type { Directive } from 'vue';
import { debounce } from 'radash';

let target: HTMLElement | null = null;

const resizeCenterFn = debounce({ delay: 300 }, () => {
  // 如果元素不存在，则直接返回
  if (!target) return;
  // 获取元素的父节点，并转换为HTMLDivElement类型
  const parentNode = target.parentNode as HTMLDivElement;
  // 获取父节点的宽度
  const containerVw = parentNode.clientWidth;
  // 获取父节点的高度
  const containerVh = parentNode.clientHeight;
  // 计算高度缩放比例
  const hScale = containerVh / 1080;
  // 计算宽度缩放比例
  const wScale = containerVw / 1920;
  // 取宽度和高度的较小值
  const scale = Math.min(wScale, hScale);
  // 计算margin-left
  const marginLeft = `${((containerVw - 1920 * scale) / 2).toFixed(4)}px`;
  // 计算margin-top
  const marginTop = `${((containerVh - 1080 * scale) / 2).toFixed(4)}px`;
  // 设置元素的样式
  target.style.cssText += `width:1920px;
        height:1080px;
        transform:  translate(${marginLeft}, ${marginTop}) scale(${scale});
        transform-origin: 0 0;
        overflow:hidden; transition: all 0.3s`;
});

const vResizeCenter: Directive<HTMLElement, any> = {
  mounted(el) {
    target = el;
    resizeCenterFn();
    window.addEventListener('resize', resizeCenterFn);
  },
  unmounted(el) {
    target = null;
    el.removeEventListener('resize', resizeCenterFn);
  },
};

export default vResizeCenter;
