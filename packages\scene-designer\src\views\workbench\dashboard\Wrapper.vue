<template>
  <div ref="el" class="bounding" :class="{ 'is-inline': isInline }">
    <div
      v-show="isActive && !workbench.isDragging"
      class="bounding-tool bounding-tool__right-top"
    >
      <a-space>
        <span class="bounding-tool__right-item" @click="handleDelete">
          <MaterialSymbolsDeleteOutline class="cursor-pointer" />
        </span>
      </a-space>
    </div>
    <div
      :class="{
        'is-active': isActive,
        'bounding-content': workbench.mode === 'design',
      }"
    >
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { CleanupFn } from '@atlaskit/pragmatic-drag-and-drop/dist/types/internal-types';
import { draggable } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import useSchemaStore from '@/scene-designer/stores/schema';
import useWorkbench from '@/scene-designer/stores/workbench';
import useConfigStore from '@/scene-designer/stores/config';

const { schema } = defineProps<{ schema?: Widget.RenderConfig }>();

const schemaStore = useSchemaStore();
const configStore = useConfigStore();
const isActive = computed(() => schema?.id === schemaStore.currentId);

const isInline = computed(
  () =>
    schema &&
    [
      'KButton',
      'KInput',
      'KImage',
      'KText',
      'KSwitch',
      'KDatePicker',
      'KRangePicker',
      'KSelect',
      'KRadio',
      'KCheckbox',
    ].includes(schema.comp)
);

const el = useTemplateRef<HTMLElement>('el');
const workbench = useWorkbench();

const cleanup = ref<CleanupFn>();

onMounted(() => {
  if (!el.value || !schema) return;
  cleanup.value = draggable({
    element: el.value,
    getInitialData: () => ({
      key: schema.comp,
      item: schema,
    }),
    onDragStart: (e: any) => {
      workbench.setIsDragging(true);
      workbench.setDraggingWidget(schema.comp);
      schemaStore.deleteSchema(schema.id, schema.parentId);
    },
    onDrop: () => {
      workbench.setIsDragging(false);
    },
  });
});

const handleDelete = () => {
  const item = schemaStore.getSchemaById(schema?.id || '');
  if (!item) return;
  schemaStore.deleteSchema(item.id, item.parentId);
  configStore.styleConfig.delete(item.id);
  configStore.propsConfig.delete(item.id);
};

onBeforeUnmount(() => {
  cleanup.value && cleanup.value();
});
</script>
<style lang="less">
@boundingGap: 4px;

.bounding {
  position: relative;
  z-index: 100;
  padding: @boundingGap;
  pointer-events: all;
  box-sizing: border-box;
  // margin: @boundingGap;

  &-tool {
    position: absolute;
    height: 8px;
    pointer-events: all;
    z-index: 200;
    font-size: 14px;
    color: #fff;
    &__right-top {
      right: 4px;
      bottom: 16px;
    }
    &__right-item {
      color: oklch(0.637 0.237 25.331);
      cursor: pointer;
    }
  }
}
.is-active {
  border: 1px solid var(--primary-100);
  border-radius: 5px;
}

.is-inline {
  display: inline-block;
}
.bounding-content {
  &:hover {
    border: 1px solid var(--primary-100);
    border-radius: 5px;
  }
}
</style>
