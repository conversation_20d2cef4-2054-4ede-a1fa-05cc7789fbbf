import { Select, SelectProps } from 'ant-design-vue';
export default ({
  label,
  style,
  ...props
}: SelectProps & {
  label: string;
  style: Record<string, any>;
}) => {
  return (
    <div
      style={{
        display: 'inline-flex',
        alignItems: 'center',
      }}
    >
      {label && (
        <label style="white-space: nowrap;padding:0 8px;">{label}:</label>
      )}
      <Select {...props} style={{ minWidth: `220px` }} />
    </div>
  );
};
