declare namespace Widget {
  enum WidgetType {
    BASIC = 'basic',
    LAYOUT = 'layout',
    AUXILIARY = 'auxiliary',
    FORM = 'form',
  }

  type TypeEnum = keyof typeof WidgetType;
  type BaseValueType<T = any> = string | number | boolean | T[] | any;

  interface RenderConfig {
    id: string;
    comp: string;
    props: Record<string, any>;
    style: Record<string, any>;
    children?: RenderConfig[];
    parentId?: string;
  }

  interface StyleConfig extends PropsConfig {}

  interface PropsConfig {
    [key: string]: {
      label: string;
      comp: string;
      value: BaseType;
      compProps?: {
        options?: Global.GroupOption[] | Global.Option[];
        [x: string]: any;
      };
    };
  }

  interface ResourceConfig {
    key: string;
    name: string;
    icon: string;
    type: TypeEnum;
    description?: string;
    isDisabled?: boolean;
  }

  interface WidgetGroup {
    title: string;
    widgets: ResourceConfig[];
  }
  interface WidgetGroups {
    [key: WidgetType]: WidgetGroup;
  }
}
