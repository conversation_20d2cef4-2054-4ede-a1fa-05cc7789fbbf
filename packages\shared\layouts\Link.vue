<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>
<script lang="ts" setup>
import { utils } from '@kd/utils';
/**
 * @description 可用作外部链接跳转和内部路由跳转的组件
 */
const props = defineProps<{ to: string }>();
const { to } = toRefs(props);
const isExternalUrl = computed(() => utils.isExternal(to.value));

const type = computed(() => (isExternalUrl.value ? 'a' : 'router-link'));

const linkProps = (to: string) => {
  if (isExternalUrl.value) {
    return {
      href: to,
      target: '_blank',
      rel: 'noopener',
    };
  }
  return {
    to,
  };
};
</script>
