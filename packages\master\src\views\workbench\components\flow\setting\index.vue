<template>
  <div
    class="node-setting-panel"
    :class="{ 'node-setting-panel--collapsed': collapsed }"
  >
    <div class="node-setting-panel__header">
      <span class="node-setting-panel__title">
        {{ panelTitle }}
      </span>
      <div class="node-setting-panel__actions">
        <MinusOutlined
          v-if="!collapsed"
          class="node-setting-panel__action-icon"
          @click="handleCollapse"
        />
        <PlusOutlined
          v-else
          class="node-setting-panel__action-icon"
          @click="handleExpand"
        />
      </div>
    </div>
    <div v-show="!collapsed" class="node-setting-panel__content">
      <component
        :is="currentSettingComponent"
        v-if="currentSettingComponent"
        :node="currentNode"
      />
      <div v-else class="node-setting-panel__empty">
        <InfoCircleOutlined />
        <span>暂无可配置内容</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Node } from '@antv/x6';
import MinusOutlined from '~icons/ant-design/minus-outlined';
import PlusOutlined from '~icons/ant-design/plus-outlined';
import InfoCircleOutlined from '~icons/ant-design/info-circle-outlined';
import { NODE_TYPE } from '../../../constants';
import eventEmitter from 'shared/utils/eventEmitter';

// 控制面板折叠状态
const collapsed = ref(false);
// 当前选中的节点
const currentNode = ref<Node | null>(null);

eventEmitter.on('node:select', e => {
  currentNode.value = e;
});

const currentComponent = computed(() => currentNode.value?.getData());

const panelTitle = computed(() =>
  currentComponent.value
    ? ` 节点配置-${currentComponent.value.componentName}`
    : '流程配置'
);

// 动态计算当前节点对应的配置组件
const currentSettingComponent = computed(() => {
  const nodeType = currentComponent.value?.componentType;
  // 根据节点类型返回对应的配置组件
  switch (nodeType) {
    case NODE_TYPE.START:
    case NODE_TYPE.END:
      return defineAsyncComponent(() => import('./NormalNodeSetting.vue'));
    case NODE_TYPE.GROUP:
      return defineAsyncComponent(() => import('./GroupNodeSetting.vue'));
    case NODE_TYPE.LOOP:
      return defineAsyncComponent(() => import('./LoopNodeSetting.vue'));
    case NODE_TYPE.SOFT:
      return defineAsyncComponent(() => import('./SoftNodeSetting.vue'));
    case NODE_TYPE.CONDITION:
      return defineAsyncComponent(() => import('./ConditionNodeSetting.vue'));
    default:
      return defineAsyncComponent(() => import('./GlobalSetting.vue'));
  }
});

// 处理面板折叠
const handleCollapse = () => {
  collapsed.value = true;
};

// 处理面板展开
const handleExpand = () => {
  collapsed.value = false;
};
</script>

<style lang="less">
.node-setting-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
  margin: 0 24px;

  &--collapsed {
    height: 48px;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    color: #1f1f1f;
  }

  &__actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  &__action-icon {
    font-size: 14px;
    color: #595959;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: #1f1f1f;
    }
  }

  &__content {
    height: 300px;
    overflow-y: auto;
    padding: 16px;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c8c8c;
    gap: 8px;

    .anticon {
      font-size: 24px;
    }
  }
}
</style>
