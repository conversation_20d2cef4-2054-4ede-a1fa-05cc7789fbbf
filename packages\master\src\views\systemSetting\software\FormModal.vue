<template>
  <a-modal
    v-model:open="dialog.visible"
    :title="isNew ? '新建' : '编辑'"
    width="30%"
    :destroy-on-close="true"
    @cancel="dialog.cancel"
  >
    <a-form
      ref="formRef"
      :model="info"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="名称" name="name">
        <a-input v-model:value="info.name" placeholder="请输入专业软件名称" />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-input v-model:value="info.name" placeholder="请输入专业软件状态" />
      </a-form-item>
    </a-form>
    <template #footer>
      <div>
        <a-button @click="dialog.cancel">取消</a-button>
        <a-button type="primary" @click="handldSubmit">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { Rule } from 'ant-design-vue/es/form';
import { useDialog } from 'dialog-async';
import { debounce } from 'radash';
import { ref } from 'vue';

const { isNew, modelId, command, getList } = defineProps<{
  isNew: boolean;
  modelId?: string;
  command?: any;
  getList?: () => void;
}>();

const dialog = useDialog();

const formRef = ref();

const info = ref({
  name: '',
  status: '',
});

const rules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入专业软件名称', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请输入专业软件状态', trigger: 'change' },
  ],
};

const handldSubmit = debounce({ delay: 200 }, () => {
  formRef.value
    .validate()
    .then(async () => {
      console.log('submit :>>');
    })
    .catch((error: any) => {
      console.log('error :>>', error);
    });
});

// onMounted(() => {
//   if (command) info.value = command;
// });
</script>
<style scoped lang="less"></style>
