<template>
  <a-layout>
    <a-layout-header style="background-color: var(--primary-color)">
      <div class="header-wrapper">
        <div class="header-left">
          <img
            class="align-[-18px]"
            style="height: 48px"
            src="./logo.png"
            alt="logo"
          />
          <h1 class="ml-2" style="font-size: 20px; font-weight: bold">
            {{ title }}
          </h1>
        </div>
        <slot name="right">
          <a-dropdown>
            <span class="ant-dropdown-link" @click.prevent>
              您好，{{ userInfo.userName }}
            </span>
          </a-dropdown>
        </slot>
      </div>
    </a-layout-header>
    <a-layout>
      <a-layout-sider
        v-if="showSider"
        v-model:collapsed="collapsed"
        collapsible
        theme="light"
      >
        <a-menu
          v-model:selected-keys="selectedKeys"
          theme="light"
          mode="inline"
        >
          <sidebar-item
            v-for="item in menus"
            :key="item.path"
            :item="item"
            :base-path="rootMenu!.path"
          />
        </a-menu>
      </a-layout-sider>
      <a-layout>
        <div v-if="showBreadcrumb" class="breadcrumb-nav">
          <a-breadcrumb>
            <a-breadcrumb-item v-for="item in matched" :key="item.path">
              {{ item.meta?.title }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <a-layout-content class="content">
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>
<script lang="ts" setup>
import useUserStore from '../store/user';
import SidebarItem from './SidebarItem.vue';

const { showSider } = defineProps({
  showSider: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '',
  },
  showBreadcrumb: {
    type: Boolean,
    default: false,
  },
});

const collapsed = ref(false);
const { userInfo } = useUserStore();
const router = useRouter();
const { routes } = router.options;
const matched = computed(() =>
  (router.currentRoute.value.matched || []).filter(t => t.path === '/')
);
const route = router.currentRoute;
const selectedKeys = ref<string[]>([route.value.path]);
const rootMenu = routes.find(k => k.meta!.isRoot);
const menus = computed(() =>
  (rootMenu?.children || []).filter(t => t.meta?.showInMenu !== false)
);

// Watch for route changes and update selectedKeys
watch(
  () => route.value.path,
  newPath => {
    selectedKeys.value = [newPath];
  }
);
</script>
<style lang="less">
.ant-layout-header {
  padding: 0 20px;
}
.header-wrapper {
  display: flex;
  justify-content: space-between;
  height: 100%;
  color: #fff;
  .header-left {
    display: flex;
    align-items: center;
    &-title {
      color: inherit;
      margin: 0;
      padding: 0 10px;
    }
  }
}
.breadcrumb-nav {
  display: flex;
  align-items: center;
}

.content {
  height: calc(100vh - 80px);
  margin: 8px;
  padding: 8px;
  border-radius: 10px;
  background-color: #fff;
  overflow: hidden;
  box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  & > .ant-breadcrumb {
    padding: 5px 0;
  }
}
</style>
