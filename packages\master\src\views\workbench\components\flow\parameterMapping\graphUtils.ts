/**
 * @file graphUtils.ts
 * @description Utility functions for graph creation and configuration
 */

import { Graph } from '@antv/x6';
import { EDGE_COLOR } from './constants';

/**
 * Creates a new graph instance
 * @param container - The container element
 * @param width - The width of the graph
 * @param height - The height of the graph
 * @returns The created graph instance
 */
export function createGraph(container: HTMLElement): Graph {
  return new Graph({
    container,
    grid: true,
    autoResize: true,
    panning: true, // Allow canvas dragging
    mousewheel: true, // Allow mouse wheel zooming
    connecting: {
      connector: 'smooth',
      snap: true, // Enable auto-snapping
      allowBlank: false, // Don't allow connections to blank space
      highlight: true, // Highlight potential connection targets
      connectionPoint: 'anchor', // Connection point style
      createEdge() {
        return this.createEdge({
          attrs: {
            line: {
              stroke: EDGE_COLOR, // Edge color
              strokeWidth: 1, // Edge width
              targetMarker: {
                name: 'block', // Arrow style
                size: 8, // Arrow size
              },
            },
          },
        });
      },
    },
  });
}

/**
 * Handles graph zooming
 * @param graph - The graph instance
 * @param zoomFactor - The zoom factor to apply (positive for zoom in, negative for zoom out)
 * @returns The new zoom level
 */
export function handleZoom(graph: Graph, zoomFactor: number): number {
  const newZoom = graph.zoom() + zoomFactor;
  graph.zoomTo(newZoom);
  return newZoom;
}

/**
 * Resets graph zoom to 1
 * @param graph - The graph instance
 */
export function resetZoom(graph: Graph): void {
  graph.zoomTo(1);
}

/**
 * Removes an edge from the graph
 * @param graph - The graph instance
 * @param edgeId - The ID of the edge to remove
 */
export function removeEdge(graph: Graph, edgeId: string): void {
  graph.removeEdge(edgeId);
}
