import type { App } from 'vue';
import KInput from './KInput';
import KButton from './KButton';
import KImage from './KImage';
import KText from './KText';
import KEmpty from './KEmpty';
import KC<PERSON>r from './KContainer';
import KGrid from './KGrid';
import ColorPicker from './ColorPicker';
import Uploader from './Uploader';
import ImageSelect from './ImageSelect';
import SelectModal from './SelectModal';
import KCard from './KCard';
import KSwitch from './KSwitch';
import KDatePicker from './KDatePicker';
import KRangePicker from './KRangePicker';
import KSelect from './KSelect';
import RenderSelect from './KSelect/RenderSelect';
import KRadio from './KRadio';
import RenderRadio from './KRadio/RenderRadio';
import KCheckbox from './KCheckbox';
import RenderCheckbox from './KCheckbox/RenderCheckbox';
import KB<PERSON> from './KBinder';
import {
  Input,
  InputNumber,
  Select,
  Slider,
  Radio,
  RadioGroup,
} from 'ant-design-vue';

export default {
  install(app: App): void {
    app.component('KEmpty', KEmpty);
    app.component('AInput', Input);
    app.component('ASelect', Select);
    app.component('ASlider', Slider);
    app.component('ARadio', Radio);
    app.component('ARadioGroup', RadioGroup);
    app.component('AInputNumber', InputNumber);
    app.component('KInput', KInput);
    app.component('KButton', KButton);
    app.component('KImage', KImage);
    app.component('KText', KText);
    app.component('KContainer', KContainer);
    app.component('KGrid', KGrid);
    app.component('ColorPicker', ColorPicker);
    app.component('Uploader', Uploader);
    app.component('ImageSelect', ImageSelect);
    app.component('SelectModal', SelectModal);
    app.component('KCard', KCard);
    app.component('KBinder', KBinder);
    app.component('KSwitch', KSwitch);
    app.component('KDatePicker', KDatePicker);
    app.component('KRangePicker', KRangePicker);
    app.component('KSelect', KSelect);
    app.component('RenderSelect', RenderSelect);
    app.component('KRadio', KRadio);
    app.component('RenderRadio', RenderRadio);
    app.component('KCheckbox', KCheckbox);
    app.component('RenderCheckbox', RenderCheckbox);
  },
};
