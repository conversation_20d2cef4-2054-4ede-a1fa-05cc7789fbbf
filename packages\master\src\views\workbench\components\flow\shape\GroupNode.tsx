/**
 * @file GroupNode.tsx
 * @description 分组节点组件，用于在工作流图中显示可包含其他节点的分组
 */

import { Graph, Node } from '@antv/x6';
import style from './groupNode.module.css';
import { NODE_TYPE } from '../../../constants';
import BxCaretDown from '~icons/bx/caret-down';
/**
 * 分组节点组件
 */
export default defineComponent({
  name: 'GroupNode',
  props: {
    /** 节点实例 */
    node: {
      type: Object as PropType<Node>,
      required: true,
    },
    /** 图形实例 */
    graph: Object as PropType<Graph>,
  },
  setup(props) {
    const isCollapsed = ref(false);
    const originSize = ref(props.node.getSize());
    // 节点数据
    const data = ref(props.node.getData() || {});
    // 监听节点数据变化
    props.node.on('change:data', ({ current }) => {
      data.value = current;
    });

    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value;
      const size = props.node?.getSize()!;
      props.node?.resize(
        size.width,
        isCollapsed.value ? 40 : originSize.value.height
      );
      const children = props.node.getChildren();
      children?.forEach(cell => {
        if (isCollapsed.value) {
          cell.hide();
        } else {
          cell.show();
        }
      });
    };

    return { data, isCollapsed, toggleCollapse };
  },

  render() {
    return (
      <div
        class={[style.groupNode, this.data.isSelected ? style.selected : '']}
      >
        <div class={style.nodeHeader}>
          <span class={style.nodeName}>
            {this.data.componentType === NODE_TYPE.LOOP ? '循环' : '容器'}
          </span>
          <div class={style.nodeHeaderIcon}>
            <BxCaretDown
              class={this.isCollapsed ? style.transform : null}
              onClick={this.toggleCollapse}
            />
          </div>
        </div>
        <span class={style.businessNodeName}> {this.data?.componentName}</span>
        {/* 子流程容器 */}
        <div class={style.subflowContainer}></div>
      </div>
    );
  },
});
