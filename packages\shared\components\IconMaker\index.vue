<template>
  <div class="icon-select" @click="openModal">
    <!-- Display selected icon -->
    <div class="preview-circle">
      <template v-if="value">
        <!-- Text icon -->
        <div
          v-if="value.type === 'text'"
          class="text-icon"
          :style="{
            backgroundColor: value.backgroundColor,
            color: value.color,
          }"
        >
          {{ value.content }}
        </div>
        <!-- Image icon -->
        <div v-else-if="value.type === 'image'" class="image-icon">
          <img :src="value.content" alt="Icon" />
        </div>
      </template>
      <div v-else class="placeholder-icon">
        <PlusOutlined />
        <span>选择图标</span>
      </div>
    </div>

    <!-- Icon selection modal -->
    <a-modal
      v-model:open="modalVisible"
      title="选择图标"
      width="700px"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <a-tabs :activeKey="tempIcon.type" @change="handleTabChange">
        <!-- Text icon tab -->
        <a-tab-pane key="text" tab="文字图标">
          <a-form :label-col="{ style: 'width: 80px' }">
            <a-form-item label="文字" required>
              <a-input
                v-model:value="tempIcon.content"
                placeholder="请输入文字（建议1-2个字）"
                :maxlength="2"
              />
            </a-form-item>
            <a-form-item label="背景色">
              <input
                type="color"
                :value="tempIcon.backgroundColor"
                @input="e => updateTextIcon(e, 'backgroundColor')"
              />
            </a-form-item>
            <a-form-item label="文字颜色">
              <input
                type="color"
                :value="tempIcon.color"
                @input="e => updateTextIcon(e, 'color')"
              />
            </a-form-item>
          </a-form>
          <div class="preview-container">
            <div class="preview-title">预览</div>
            <div class="preview-circle">
              <div
                class="text-icon"
                :style="{
                  backgroundColor: tempIcon.backgroundColor,
                  color: tempIcon.color,
                }"
              >
                {{ tempIcon.content }}
              </div>
            </div>
          </div>
        </a-tab-pane>
        <!-- Image icon tab -->
        <a-tab-pane key="image" tab="图片图标">
          <div class="image-upload">
            <a-upload
              v-model:file-list="fileList"
              list-type="picture-card"
              :max-count="1"
              :before-upload="beforeUpload"
              @change="handleImageChange"
            >
              <div>
                <PlusOutlined />
                <div class="mt-2">上传</div>
              </div>
            </a-upload>

            <div class="image-crop-area">
              <div class="crop-title">裁剪图片</div>
              <div class="cropper-container">
                <Cropper
                  ref="cropperRef"
                  :src="imageUrl"
                  :stencil-props="{
                    aspectRatio: 1,
                    class: 'circle-stencil',
                  }"
                  :stencil-component="CircleStencil"
                  @change="onCropperChange"
                />
              </div>
              <div class="preview-container">
                <div class="preview-title">预览</div>
                <div class="preview-circle">
                  <img v-if="previewUrl" :src="previewUrl" alt="预览" />
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import { message, Upload } from 'ant-design-vue';
import PlusOutlined from '~icons/ant-design/plus-outlined';
import { Cropper, CircleStencil } from 'vue-advanced-cropper';
import 'vue-advanced-cropper/dist/style.css';
import './index.css';

interface IconValue {
  /** 图标类型: 'text' | 'image' */
  type: 'text' | 'image';
  /** 图标内容: 文本或图片URL */
  content: string;
  /** 图标背景色 */
  backgroundColor?: string;
  /** 图标文本颜色 */
  color?: string;
}

// Define props and emits
const props = defineProps<{
  value?: IconValue;
}>();

const emit = defineEmits<{
  'update:value': [value: IconValue];
  change: [value: IconValue];
}>();

// Modal visibility state
const modalVisible = ref(false);

// Temporary icon state for editing
const tempIcon = ref<IconValue>({
  type: 'text',
  content: '',
  backgroundColor: '#1890ff',
  color: '#ffffff',
});

// Image upload and cropping
const fileList = ref([]);
const imageUrl = ref<string>('');
const previewUrl = ref<string>('');
const cropperRef = ref<InstanceType<typeof Cropper>>();

// 裁剪器变更事件处理
const onCropperChange = ({ canvas }: any) => {
  if (canvas) {
    // 获取裁剪后的图片数据
    previewUrl.value = canvas.toDataURL('image/webp', 0.8);

    // 更新临时图标数据
    tempIcon.value = {
      type: 'image',
      content: previewUrl.value,
    };
  }
};

// Open the modal and initialize tempIcon
const openModal = () => {
  if (props.value) {
    tempIcon.value = { ...props.value };
  } else {
    tempIcon.value = {
      type: 'text',
      content: '文字',
      backgroundColor: '#1890ff',
      color: '#ffffff',
    };
  }
  modalVisible.value = true;
};

// Update text icon when text or colors change
const updateTextIcon = (e: any, field: keyof typeof tempIcon.value) => {
  tempIcon.value[field] = e.target.value;
};

// 处理图片上传
const beforeUpload: UploadProps['beforeUpload'] = file => {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('只能上传图片文件!');
    return Upload.LIST_IGNORE;
  }

  // 读取文件并显示图片
  const reader = new FileReader();
  reader.onload = e => {
    imageUrl.value = e.target?.result as string;

    // 重置预览图片
    previewUrl.value = '';

    // 重置临时图标类型为图片
    tempIcon.value = {
      type: 'image',
      content: '',
    };

    nextTick(() => {
      // 在下一个 tick 初始化裁剪器，确保 DOM 已更新
      if (cropperRef.value) {
        cropperRef.value.refresh();
      }
    });
  };
  reader.onerror = () => {
    message.error('读取图片文件失败!');
  };
  reader.readAsDataURL(file);

  return false; // 返回 false 阻止自动上传但保留文件在列表中
};

const handleImageChange = (info: UploadChangeParam) => {
  // 这个函数仍然会被 a-upload 调用
  // 但我们已经在 beforeUpload 中处理了文件读取
  // 我们可以使用这个来更新 UI 状态（如果需要）
  if (info.file.status === 'error') {
    message.error('上传图片失败!');
  }
};

// 确认选择
const handleConfirm = () => {
  // 根据当前活动标签进行验证
  if (tempIcon.value.type === 'text') {
    // 对于文字图标，确保有内容
    if (!tempIcon.value.content) {
      message.warning('请输入文字内容');
      return;
    }
  } else {
    // 对于图片图标，确保有图片
    if (!imageUrl.value) {
      message.warning('请先上传图片');
      return;
    }

    // 确保已经裁剪了图片
    if (!previewUrl.value || !tempIcon.value.content) {
      message.warning('请先裁剪图片');
      return;
    }

    // 确保图标类型正确
    if (tempIcon.value.type !== 'image') {
      tempIcon.value.type = 'image';
    }
  }

  // 发送更新的图标
  emit('update:value', tempIcon.value);
  emit('change', tempIcon.value);

  // 关闭模态框
  modalVisible.value = false;
};

const handleTabChange = (key: any) => {
  tempIcon.value.type = key;
  tempIcon.value.content = '文字';
};

// 取消选择
const handleCancel = () => {
  // 重置图片上传状态
  imageUrl.value = '';
  previewUrl.value = '';
  fileList.value = [];

  // 关闭模态框
  modalVisible.value = false;
};
</script>
