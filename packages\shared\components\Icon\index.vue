<template>
  <div
    v-if="value.type === 'text'"
    class="text-icon"
    :style="{
      backgroundColor: value.backgroundColor,
      color: value.color,
    }"
  >
    {{ value.content }}
  </div>
  <!-- Image icon -->
  <div v-else-if="value.type === 'image'" class="image-icon">
    <img :src="value.content" alt="Icon" />
  </div>
</template>
<script lang="ts" setup>
interface IconValue {
  type: 'text' | 'image';
  /** 图标内容: 文本或图片URL */
  content: string;
  /** 图标背景色 */
  backgroundColor?: string;
  /** 图标文本颜色 */
  color?: string;
}

defineProps<{
  value: IconValue;
}>();
</script>
<style>
.text-icon {
  width: 100%;
  height: 100%;
  text-align: center;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
}

.image-icon {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
