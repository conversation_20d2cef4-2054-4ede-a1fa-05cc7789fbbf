---
description: 
globs: 
alwaysApply: true
---

# 项目规范 
⼀、命名规范 
常⽤的命名规范： 
camelCase（⼩驼峰式命名法 —— ⾸字⺟⼩写） 
PascalCase（⼤驼峰式命名法 —— ⾸字⺟⼤写） 
kebab-case（短横线连接式） 
Snake （下划线连接式） 
## 项⽬⽂件命名 
### 项⽬名 
全部采⽤⼩写⽅式， 以短横线”-”分隔。例：my-project-name。 
### ⽬录名 
参照项⽬命名规则，有复数结构时，要采⽤复数命名法。例如：docs、assets、 
components、directives、mixins、utils、views。 
### 图像⽂件命名 
全部采⽤⼩写⽅式，优先选择单个单词命名，多个单词命名以下划线”_”分割。
banner.jpg 
menu_aboutus.png 
menutitle_news.gif 
logo_police.jpg 
logo_national.webp 
pic_people.jpg
### HTML⽂件名 
全部采⽤⼩写⽅式，优先选择单个单词命名，多个单词命名以下划线“_”分割。 
index.html
error_report.html
### CSS⽂件命名 
全部采⽤⼩写⽅式，优先选择单个单词命名，多个单词以短横线“-”分割。 
normalize.css 
base.less 
date-picker.scss 
### Javascript⽂件名 
全部采⽤⼩写⽅式，优先选择单个单词命名，多个单词以短横线“-”分割。 
index.js 
date-util.js 
account-model.ts 
## Vue组件命名 
### 单⽂件组件名 
⽂件扩展名为.vue的single-file component(单⽂件组件)，命名应该始终是⼤写单词开 
头(PascalCase)。 
 MyComponent.vue 
### 业务组件名 
业务组件：它不像基础组件只包含某个功能，⽽是在业务中被多个⻚⾯复⽤的（具有 
可复⽤性），它与基础组件的区别是，业务组件只在当前项⽬中会⽤到，不具有通⽤ 
性，⽽且会包含⼀些业务，⽐如数据请求；⽽基础组件不含业务，在任何项⽬中都可 
以使⽤，功能单⼀，⽐如⼀个具有数据校验功能的输⼊框。 
掺杂了复杂业务的组件（拥有⾃⾝ data、prop 的相关处理）即业务组件应该以 
Custom 前缀命名。 
CustomCard.vue前端开发规范 
### 紧密耦合的组件名 
和⽗组件紧密耦合的⼦组件应该以⽗组件名作为前缀命名。因为编辑器通常会按照字 
⺟顺序组织⽂件，所以这样做可以把相关联的⽂件排在⼀起。 
TodoList.vue 
TodoListItem.vue 
TodoListItemButton.vue 
### 完整单词的组件名 
组件名应该倾向于完整单词⽽不是缩写。编辑器中的⾃动补全已经让书写⻓命名的代 
价⾮常低了，⽽带来的明确性确实⾮常宝贵的。  
StudentDashboardSettings.vue 
UserProfileOptions.vue 
## 代码参数命名 
### props 
在声明prop的时候，其命名应该始终是camelCase，⽽在模版和jsx中应该始终是 
kebab-case。
``` 
<WelcomeMessage greetting-text=”hi” /> 
export default { 
    name: “MyComponent”, 
    props: { 
        greetingText: { 
            type: String, 
            required: true 
        } 
    } 
}
``` 
### router 
Vue Router Path命名采⽤kebab-case格式。⽤Snake(如/user_info)或者 
camelCase（如：/userInfo）的单词会被当成⼀个单词，搜索引擎⽆法区分语义。 
### 模版中组件前端开发规范 
对于绝⼤多数项⽬来说，在单⽂件组件和字符串模版中的组件名应该总是PascalCase 
的，但是在DOM模版中总是kebab-case的。 
在单⽂件组件和字符串模板中:
<MyComponent/> 
 在 DOM 模板中:
<my-component></my-component>

### ⾃闭合组件 
在单⽂件组件、字符串模版和JSX中没有内容的组件应该是⾃闭合的。 
在单⽂件组件和字符串模板中
<MyComponent/>

### 变量 
命名⽅法：camelCase 
命名规范：类型+对象或属性的⽅式 
// bad 
var getTitle = "LoginTable" 
// good 
let tableTitle = "LoginTable" 
let mySchool = "我的学校" 
### 常量 
命名⽅法：全部⼤写下划线分割 
命名规范 适⽤⼤写字⺟合下划线来组合命名，下划线⽤来分割单词 
const MAX_COUNT = 10 
const URL = 'http://test.host.com'
### ⽅法 
命名⽅法：camelCase 
命名规范：统⼀使⽤动词或者动词+名词的形式 
普通情况下，使⽤动词+名词的形式前端开发规范 
// bad 
go、nextPage、show、open、login 
// good 
jumpPage、openCarInfoDialog 
请求数据⽅法，以data结尾 
// bad 
takeData、confirmData、getList,postForm 
// good 
getListData、postFormData 

### ⾃定义事件 
⾃定义事件应始终使⽤kebab-case的事件名，不同于组件和props，事件名不存在任 
何⾃动化的⼤⼩写转换。触发的事件名需要完全匹配监听这个事件所⽤的名称。 
this.$emit('my-event') 
<MyComponent @my-event="handleDoSomething" /> 
不同于组件和 prop，事件名不会被⽤作⼀个 JavaScript 变量名或 property 名，所以 
就没有理由使⽤ camelCase 或 PascalCase 了。并且 v-on 事件监听器在 DOM 模板 
中会被⾃动转换为全⼩写 (因为 HTML 是⼤⼩写不敏感的)，所以 v-on:myEvent 将会 
变成 v-on:myevent——导致 myEvent 不可能被监听到。

```
<div 
@blur="toggleHeaderBlur" 
@focus="toggleHeaderFocus" 
@click="toggleMenu" 
@keydown.esc="handleKeydown" 
@keydown.enter="handleKeydown" 
@keydown.up.prevent="handleKeydown" 
@keydown.down.prevent="handleKeydown" 
@keydown.tab="handleKeydown" 
@keydown.delete="handleKeydown"前端开发规范 
6 
@mouseenter="hasMouseHoverHead = true" 
@mouseleave="hasMouseHoverHead = false"> 
</div> 
```
⽽为了区分原⽣事件和⾃定义事件在 Vue 中的使⽤，建议除了多单词事件名使⽤ 
kebab-case 的情况下，命名还需遵守为 on + 动词 的形式，如下：
``` 
<!-- ⽗组件 --> 
<div 
@on-search="handleSearch" 
@on-clear="handleClear" 
@on-clickoutside="handleClickOutside"> 
</div> 
// ⼦组件 
export default { 
    methods: { 
        handleTriggerItem () { 
            this.$emit('on-clear') 
        } 
    } 
}
``` 
# 代码规范 
## Vue 
### prop 
prop的定义应该尽量详细。
``` 
export default { 
    props: { 
        status: { 
            type: String, 
            required: true, 
            validator: function(value) { 
                return [‘syncing’,’synced’,’error’].indexOf(value) !== -1 
            } 
        } 
    } 
}
``` 
### computed 
应该把复杂计算属性分割为尽可能多的更简单的属性。前端开发规范 
### 为v-for设置key值 
在组件上必须⽤key搭配v-for，以便维护内部组件机器⼦树的状态。
``` 
<ul> 
    <li 
        v-for="todo in todos" 
        :key="todo.id"
    > 
        {{ todo.text }} 
    </li> 
</ul>
``` 
2.1.4 多个 attribute 的元素 
多个 attribute 的元素应该分多⾏撰写，每个 attribute ⼀⾏。
``` 
<!-- bad --> 
<img src="https://vuejs.org/images/logo.png" alt="Vue Logo"> 
<MyComponent foo="a" bar="b" baz="c"/> 
<!-- good --> 
<img 
    src="https://vuejs.org/images/logo.png" 
    alt="Vue Logo"
> 
<MyComponent 
    foo="a" 
    bar="b" 
    baz="c"
/>
``` 
### 模版中简单的表达式 
组件模版应该只包含简单的表达式，复杂的表达式则应该重构为计算属性或⽅法。复 
杂表达式会让你的模版变得不那么声明式。我们应该尽量描述出现的是什么，⽽⾮如 
何计算那个值。⽽且计算属性和⽅法可以使代码重⽤。
``` 
// bad 
{{ 
    fullName.split(' ').map((word) => { 
    return word[0].toUpperCase() + word.slice(1) 
    }).join(' ') 
}}
更好的做法：<!-- 在模板中 --> 
{{ normalizedFullName }}// 复杂表达式已经移⼊⼀个计算属性 
const normalizedFullName = computed(() => this.fullName.split(' ').map((word) => word[0].toUpperCase() + word.slice(1)).join(' ')) 
``` 
### 指令缩写
``` 
⽤ : 表⽰ v-bind: 
⽤ @ 表⽰ v-on: 
⽤ # 表⽰ v-slot: 
<input 
    :value="newTodoText" 
    :placeholder="newTodoInstructions"
> 
<input 
    @input="onInput" 
    @focus="onFocus"
> 
    <template #header> 
    <h1>Here might be a page title</h1> 
    </template> 
    <template #footer> 
    <p>Here's some contact info</p> 
    </template>
</input>     
```
## Typescript 
### 命名规范 
使⽤ camelCase 为属性或本地变量命名。 
使⽤ camelCase 为函数命名。 
使⽤ PascalCase 为类型命名。 
不要使⽤ I 做为接⼝名前缀。 
使⽤ PascalCase 为枚举值命名。 
不要为私有属性名添加 _ 前缀，使⽤ private 修辞符。 
尽可能使⽤完整的单词拼写命名。 
### 变量前端开发规范 
变量使⽤camelCase⽅式命名
``` 
// Bad 
const Foo = 1 
const UserList:string[] = [] 
// Good 
const foo = 1 
const userList:string[] = [] 
2.2.2 函数 
函数使⽤camelCase⽅式命名 
// Bad 
function Foo() {} 
// Good 
function foo() {} 
2.2.3 类 
类本⾝使⽤PascalCase⽅式命名，类成员使⽤camelCase⽅式命名。 
// Bad 
class Foo { 
Bar: number; 
Baz(): number {} 
} 
// Good 
class Foo { 
bar: number; 
baz(): number {} 
}
``` 
### 接⼝interface 
接⼝本⾝使⽤PascalCase⽅式命名，不要在接⼝前加 I ,接⼝成员使⽤camelCase⽅ 
式命名。 
在TS中，类可以实现接⼝，接⼝可以继承类。类和接⼝都是某种意义上的抽象和封 
装，继承时不需要关系他是⼀个接⼝还是⼀个类。如果使⽤ I 前缀，当⼀个变量的类 
型更改了，⽐如由接⼝变成了类，那变量名称就必须同步更改，如果不加这个前缀， 
⽆需额外做任何事情 
```
// Bad 
interface IFoo { 
Bar: number; 
Baz(): number; 
} 
// Good 
interface Foo { 
bar: number; 
baz(): number; 
} 
2.2.5 枚举 
枚举对象本⾝和枚举成员都使⽤PascalCase⽅式命名 
// Bad 
enum Status { 
success = 'success' 
} 
// Good 
enum Status { 
Success = 'success' 
} 
```
## 类型声明规范 
在进⾏类型声明时，应尽量依靠TS的⾃动类型推断功能，如果能够推断出正确类型尽 
量不要再⼿动声明。 
### 变量 
基础类型变量不需要⼿动声明类型，引⽤类型变量应该保证类型正确，不正确的需要 
⼿动声明。
```
let foo = 'foo' // ⾃动推断出“foo”类型 
let bar = 2 // ⾃动推断number类型 
let baz = false // ⾃动推断出boolean类型 
// ⾃动推断 
let foo = [1, 2] // number[] 
// Bad 
let bar = [] // 如果不指定成员类型，会被推断成any[]前端开发规范 
11 
// Good 
let bar:number[] = []
``` 
### 函数 
不要为返回值被忽略的函数设置 any 类型返回值，应该设置为 void 。使⽤ void 
相对安全，因为它能防⽌不⼩⼼使⽤了未经检查的 x 的返回值：
``` 
// Bad 
function fn(x: () => any) { 
x(); 
} 
// Good 
function fn(x: () => void) { 
x(); 
} 
```
### 命名空间 
由于es6 module的兴起，TS中原始namespace的写法已经逐渐被废弃，在业务中应 
使⽤module代替。 
### 常量集合 
使⽤对象定义的普通的常量集合在修改时不会提⽰错误，除⾮⼿动 as const` 
// Bad Success属性会被推断为string类型
``` 
const Status = { 
Success: 'success' 
} 
// Good Success属性会被推断为'success'类型 
enum Status { 
Success = 'success' 
}
``` 
### 定义⽂件（d.ts） 
1. 全局类型或变量统⼀写在 global.d.ts ⽂件中，在写⼊时需要判断： 
如果有引⼊外部模块，使⽤ declare global {} 形式定义 
```
import { StateType } from './state' 
declare global { 
export const globalState: StateType 
export const foo: string
export type AsyncFunction = (...args: any[]) => Promise<any> 
} 
```
如果没有引⼊外部模块，直接使⽤ declare 定义 
```
interface StateType {} 
declare const globalState: StateType 
declare const foo: string 
declare type AsyncFunction = (...args: any[]) => Promise<any> 
```
为第三⽅库定义扩展⽂件应该以 [package].d.ts 规则命名，⽂件统⼀放在项⽬的 
类型⽬录下。例如：
``` 
// types/references/react-redux.d.ts 
// 最好加⼀句这段话，不然导出可能会被覆盖掉，只有 DefaultRootState 存在 
export * from 'react-redux' 
import { FooState } from './foo' 
// 扩展第三⽅库 
declare module "react-redux" { 
// 原来的 DefaultRootState 的定义类型为 {}，我们把它变成索引类型 
export interface DefaultRootState { 
foo: FooState 
[key: string]: any 
} 
} 
```
### 注释 
在为接⼝或sdk书写注释时使⽤ tsdoc 的形式

