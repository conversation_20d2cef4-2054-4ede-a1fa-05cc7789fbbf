.klsz-baseui {
  // 单选框
  .ant-radio-inner {
    border-color: #c1c2c1;
  }

  .ant-radio-disabled .ant-radio-inner {
    background-color: #e9edf0;
    border-color: #c1c2c1 !important;
  }

  .ant-radio-disabled .ant-radio-inner::after {
    background-color: #e9edf0;
  }

  // 下拉选择框
  .ant-select-selection-placeholder {
    color: #c1c2c1;
    font-size: 16px;
  }

  .ant-select-selection-item {
    font-size: 14px;
  }

  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
    .ant-select-selector {
    background: #eff0f4;
    font-size: 14px;
  }

  // 时间选择器
  .ant-picker {
    border-color: #e9edf0;
    height: 32px;
  }

  // 卡片
  .ant-card-head {
    font-weight: bold;
    border-bottom: 1px solid #eff0f4;
  }

  .ant-card-body {
    padding: 16px;
  }

  // Tag
  .ant-tag {
    height: 22px;
  }

  // Modal
  .ant-modal-close:hover {
    background: white;
  }
  .ant-modal-content {
    padding: 18px 22px;
    padding-bottom: 0px;
    padding-top: 0px;
  }
  .ant-modal-header {
    padding: 12px 20px;
  }
  .ant-dropdown {
    border-radius: 4px;
    border: 1px solid #f0f2f5;
  }
  // .ant-modal-header {
  //   height: 38px;
  //   box-sizing: border-box;
  //   border-bottom: 1px solid #eff0f4;
  // }

  // .ant-modal-title {
  //   color: #333;
  //   font-weight: bold;
  // }

  // .ant-modal-close-x {
  //   width: 52px;
  //   height: 52px;
  // }

  // .ant-modal-body {
  //   padding: 30px 24px;
  // }

  // .ant-modal-footer {
  //   border-top: 1px solid #eff0f4;
  //   height: 52px;
  //   box-sizing: border-box;
  // }

  // 进度条背景色
  .ant-progress-inner {
    background: #e9edf0;
  }

  .ant-progress-text {
    color: #999;
    font-size: 12px;
  }

  .ant-input-affix-wrapper-focused {
    .ant-input-prefix {
      color: #2470ff;
    }
  }

  .ant-input {
    padding: 4px 12px;
  }
  .ant-table {
    .ant-table-tbody > tr > td {
      font-size: 12px;
      border-bottom-color: #e9edf0;
    }
    .ant-table-row {
      &.table-striped {
        background-color: #fbfcfc;
      }
    }
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td,
    .ant-table tfoot > tr > th,
    .ant-table tfoot > tr > td {
      padding: 12px 16px;
    }
    .ant-table-tbody > tr.ant-table-row-selected > td {
      background: rgb(36 112 255 / 3%);
    }
  }
  .ant-collapse {
    border: 1px solid #d9d9d9;
  }
  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
    font-size: 16px;
    padding: 12px 18px;
  }
  .ant-collapse
    > .ant-collapse-item
    > .ant-collapse-header
    .ant-collapse-arrow {
    margin-right: 16px;
  }
  // upload
  .ant-upload.ant-upload-select-picture-card {
    background-color: transparent;
    border-color: #d9d9d9;
  }

  .ant-upload-list-item-info .anticon-loading .anticon,
  .ant-upload-list-item-info .ant-upload-text-icon .anticon {
    font-size: 16px;
  }

  .ant-upload-list-text .ant-upload-list-item-name,
  .ant-upload-list-picture .ant-upload-list-item-name {
    font-size: 12px;
  }

  .ant-upload.ant-upload-drag {
    background: transparent;
    border: 1px dashed #d9d9d9;
  }

  .ant-upload.ant-upload-drag p.ant-upload-drag-icon {
    margin-bottom: 16px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-text {
    font-size: 14px;
    margin: 0px 0px 8px;
  }

  .ant-upload.ant-upload-drag p.ant-upload-hint {
    color: #666;
    font-size: 12px;
  }

  // message消息提示框
  .ant-message-notice-content {
    padding: 9px 16px;
  }

  .ant-message-warning .anticon {
    color: #ff9900;
  }

  // tree树形组件
  .ant-tree-switcher .ant-tree-switcher-icon,
  .ant-tree-switcher .ant-select-tree-switcher-icon {
    font-size: 12px;
    color: #666;
  }

  .ant-tree .ant-tree-node-content-wrapper {
    color: #666;
    font-size: 14px;
  }

  .ant-tree-switcher {
    width: 28px;
  }

  .ant-tree-checkbox-inner {
    border-color: #c1c2c1;
  }

  .ant-tree .ant-tree-node-content-wrapper:hover {
    background-color: #eff0f4;
  }

  .ant-tree-treenode-checkbox-checked .ant-tree-node-content-wrapper:hover {
    background-color: rgba(36, 112, 255, 0.1);
  }

  .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
    border-color: #c1c2c1 !important;
    background-color: #e9edf0;
  }

  .ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
    color: #c1c2c1;
  }

  // tabs标签页
  .ant-menu-title-content {
    margin-left: 8px !important;
    margin-inline-start: 8px !important;
  }
  .ant-spin-container,
  .ant-spin-nested-loading {
    height: 100% !important;
    @apply flex-1;
  }
  .ant-drawer-header {
    background: #f9f9f9;
    padding: 5px 10px;
    .ant-drawer-title {
      font-size: 15px;
    }
    .ant-drawer-close {
      font-size: 15px;
      font-weight: bold;
      margin-inline-end: 8px;
    }
  }
  .ant-select-selection-placeholder {
    font-size: 14px;
  }
  // 菜单
  .ant-menu {
    border-inline-end: 0px !important;
    font-size: 14px;
  }
  .ant-layout {
    height: 100%;
  }
  .vxe-table--tooltip-wrapper {
    position: fixed;
  }
  .mycard {
    .ant-card-body {
      padding: 0px !important;
    }
    border: 0px !important;
  }
  .table-action {
    * {
      color: var(--theme-color);
    }
  }
  // .ant-btn-primary.ant-btn-dangerous {
  //   background-color: #ff4d4f;
  //   border-color: #ff4d4f;
  //   &:hover {
  //     border-color: #ff4d4f !important;
  //   }
  // }
  .ant-btn > span:first-child[class^='anticon'] {
    padding: 0px;
    margin-top: -2px;
    vertical-align: middle;
    padding-left: 3px;
  }
  .theme-btn {
    color: var(--theme-color);
  }
  .tips::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background-color: var(--theme-color);
    vertical-align: middle;
    margin-right: 8px;
  }
  .tips-bg::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 14px;
    background-color: var(--theme-color);
    vertical-align: middle;
    margin-right: 8px;
  }
  .tips-bg {
    color: #666666;
    font-weight: bold;
    padding: 3px 0px;
    background: linear-gradient(to right, var(--theme-passive-color), #fff 40%);
  }
  .ant-tag-radius-5 {
    border-radius: 5px !important;
    padding: 0 10px;
  }
  .ant-tag-radius-10 {
    border-radius: 10px !important;
    padding: 0 12px;
  }
  .ant-input-group .ant-input-affix-wrapper:not(:last-child) {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .my-addon .ant-input-number-group > .ant-input-number:first-child,
  .my-addon .ant-input-number-group-addon:first-child {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .my-addon .ant-input-group-addon {
    padding: 0px 9px !important;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .my-addon .ant-input-number-group-addon {
    padding: 0px 9px !important;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .ant-input-group > .ant-input:first-child,
  .ant-input-group-addon:first-child {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
  .ant-input-search
    > .ant-input-group
    > .ant-input-group-addon:last-child
    .ant-input-search-button {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .ant-radio-button-wrapper-checked:not(
      .ant-radio-button-wrapper-disabled
    ):first-child {
    border-color: var(--theme-color);
  }
  .ant-radio-button-wrapper:first-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
  }
  .ant-radio-button-wrapper:last-child {
    border-radius: 0 10px 10px 0;
  }
  .ant-radio-button-wrapper-checked:not(
      [class*=' ant-radio-button-wrapper-disabled']
    ).ant-radio-button-wrapper:first-child {
    border-right-color: var(--theme-color);
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
  }
  .ant-radio-group-solid
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
    color: #fff !important;
    background: var(--theme-color);
    border-color: var(--theme-color);
  }
  .ant-modal-close-x {
    height: 46px;
    width: 46px;
  }
  .ant-btn-icon-only > .anticon {
    display: inline-block !important;
  }
  .anticon svg {
    margin-left: -6px;
  }

  .ant-btn-link {
    padding: 0 !important;
  }
}
