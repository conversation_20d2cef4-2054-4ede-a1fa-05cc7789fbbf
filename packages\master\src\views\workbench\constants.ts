/**
 * 节点状态枚举
 */
export enum NODE_STATUS {
  /** 等待调度 */
  PENDING = 1,
  /** 等待Worker接收 */
  WAITING = 2,
  /** 运行中 */
  RUNNING = 3,
  /** 失败 */
  FAILED = 4,
  /** 成功 */
  SUCCESS = 5,
  /** 取消 */
  CANCELED = 9,
  /** 手动停止 */
  STOPPED = 10,
}

/**
 * 工作流状态枚举
 */
export enum FLOW_STATUS {
  /** 等待调度 */
  PENDING = 1,
  /** 运行中 */
  RUNNING = 2,
  /** 失败 */
  FAILED = 3,
  /** 成功 */
  SUCCESS = 4,
  /** 手动停止 */
  STOPPED = 10,
}

/**
 * 节点尺寸常量
 */
export const NODE_WIDTH = 80;
export const NODE_HEIGHT = 80;
export const GROUP_NODE_WIDTH = 240;
export const GROUP_NODE_HEIGHT = 240;
export const CONDITION_NODE_HEIGHT = 100;
/**
 * 节点类型枚举
 */
export enum NODE_TYPE {
  /** 开始节点 */
  START = 1,
  /** 软件节点 */
  SOFT = 2,
  /** 条件节点 */
  CONDITION = 3,
  /** 循环节点 */
  LOOP = 4,
  /** 群组节点 */
  GROUP = 5,
  /** 结束节点 */
  END = 9,
  /** 空节点 */
  EMPTY = '-3',
}

/**
 * 应用ID常量
 */
export const APP_ID = '16';

export enum GRAPH_SHAPE {
  NORMAL = 'IconNode',
  GROUP = 'GroupNode',
  EDGE = 'dashed-edge',
  CONDITION = 'ConditionNode',
}
