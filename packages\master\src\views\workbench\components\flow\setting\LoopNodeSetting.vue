<template>
  <div class="loop-node-setting">
    <a-form :label-col="{ style: 'width: 80px' }">
      <a-form-item label="节点名称">
        <a-input
          v-model:value="formState.componentName"
          placeholder="请输入节点名称"
          @change="handleChange"
        />
      </a-form-item>
      <a-form-item label="循环次数">
        <a-input-number
          v-model:value="formState.loopCount"
          :min="1"
          :max="999"
          placeholder="请输入循环次数"
          @change="handleChange"
        />
      </a-form-item>
      <a-form-item label="描述">
        <a-textarea
          v-model:value="formState.description"
          :rows="3"
          placeholder="请输入节点描述"
          @change="handleChange"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import type { Node } from '@antv/x6';
import type { Tool } from '@/master/types/tool';

const props = defineProps<{
  node: Node;
}>();

// 表单状态
const formState = ref({
  componentName: '',
  loopCount: 1,
  description: '',
});

// 监听节点变化
watch(
  () => props.node,
  newNode => {
    if (newNode) {
      const data = newNode.getData() as Tool & {
        cycleArray: any[];
        description: string;
      };
      formState.value = {
        componentName: data.componentName || '',
        loopCount: data.cycleArray?.length || 1,
        description: data.description || '',
      };
    }
  },
  { immediate: true }
);

// 处理表单变化
const handleChange = () => {
  if (props.node) {
    const currentData = props.node.getData();
    props.node.setData({
      ...currentData,
      ...formState.value,
      cycleArray: new Array(formState.value.loopCount).fill(''),
    });
  }
};
</script>

<style lang="less" scoped>
.loop-node-setting {
  padding: 16px;
}
</style>
