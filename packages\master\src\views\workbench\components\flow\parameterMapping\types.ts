/**
 * @file types.ts
 * @description Type definitions for the data association model
 */

import { Graph } from '@antv/x6';
import type { Ref } from 'vue';

export type { Ref };

/**
 * Node data interface
 */
export interface NodeData {
  id: string;
  fromNode: string;
  toNode: string;
  fromParam: string;
  toParam: string;
  fromName: string;
  toName: string;
  calculate: string;
  flowForward: string;
}

/**
 * Global data mapping interface
 */
export interface GlobalDataMap {
  fromNode: string;
  toNode: string;
  fromParam: string;
  toParam: string;
  calculate: string;
  flowForward: string;
  id: string;
}

/**
 * Tree node interface
 */
export interface TreeNode {
  componentname: string;
  componenttype: string | number;
  name: string;
  label: string;
  sysdescomponentid: string;
  children: TreeNodeChild[];
}

/**
 * Tree node child interface
 */
export interface TreeNodeChild {
  name: string;
  label: string;
  type: string;
  unit: string;
  value: string;
  nodeId?: string;
}

/**
 * Global parameter interface
 */
export interface GlobalParam {
  paramKey: string;
  paramValue: string;
}

/**
 * Port custom data interface
 */
export interface PortCustomData {
  id: string;
  name: string;
  value: string;
  unit: string;
  type: string;
  nodeId?: string;
}

/**
 * Edge connection event data
 */
export interface EdgeConnectedEvent {
  edge: any;
  isNew: boolean;
}

/**
 * Graph controller interface
 */
export interface GraphController {
  initNode: (container: HTMLElement, globalData?: GlobalParam[]) => void;
  disposeGraph: () => void;
  nodeData: Ref<NodeData[]>;
  globalDataMap: Ref<GlobalDataMap[]>;
  removeCell: (edgeId: string, index: number) => void;
  getListArr: (graph: Graph) => Promise<void>;
  zoomIn: () => void;
  zoomOut: () => void;
  zoomReset: () => void;
}
