<template>
  <div
    class="max-w-3xl mx-auto p-6 rounded-md border border-gray-300 border-solid"
  >
    <!-- 图标选择组件 -->

    <a-form ref="formRef" :model="processData" layout="vertical">
      <!-- 图标 -->
      <a-form-item label="图标" name="icon" required>
        <IconMaker v-model:value="processData.icon" />
      </a-form-item>

      <!-- 名称 -->
      <a-form-item label="名称" name="name" required>
        <a-input v-model:value="processData.name" placeholder="请输入名称" />
      </a-form-item>

      <!-- 说明 -->
      <a-form-item label="说明" name="description">
        <a-textarea
          v-model:value="processData.description"
          placeholder="请输入说明"
          :rows="4"
        />
      </a-form-item>

      <!-- 分组 -->
      <a-form-item label="分组" name="group" required>
        <a-select
          v-model:value="processData.group"
          placeholder="请选择分组"
          style="width: 100%"
        >
          <a-select-option value="">默认</a-select-option>
          <a-select-option value="service">其他服务类审批</a-select-option>
          <a-select-option value="hr">人事类审批</a-select-option>
          <a-select-option value="finance">财务类审批</a-select-option>
          <a-select-option value="it">IT类审批</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { FormInstance } from 'ant-design-vue';
import { useProcessContext } from '../../useProcessContext';
import IconMaker from 'shared/components/IconMaker/index.vue';

// 获取流程上下文
const { processData } = useProcessContext();

// 表单引用
const formRef = useTemplateRef<FormInstance>('formRef');
</script>
