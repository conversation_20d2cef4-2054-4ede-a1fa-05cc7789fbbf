import { dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { generateUUID } from 'shared/utils';
import useWorkbenchStore from '@/scene-designer/stores/workbench';
import useSchemaStore from '@/scene-designer/stores/schema';
import useConfigStore from '@/scene-designer/stores/config';
import KEmpty from '../KEmpty';

export default defineComponent({
  name: 'KContainer',
  props: {
    style: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    id: {
      type: String,
    },
  },
  setup(props) {
    const schemaStore = useSchemaStore();
    const attrs = useAttrs();
    const slots = useSlots();
    const styled = ref<any>({});
    const workbench = useWorkbenchStore();
    const configStore = useConfigStore();
    const el = ref();
    const isDragOver = ref(false);
    onMounted(() => {
      if (!el.value) return;
      dropTargetForElements({
        element: el.value!,
        onDragEnter: () => (isDragOver.value = true),
        onDragLeave: () => (isDragOver.value = false),
        onDrop: ({ source, location, self }: any) => {
          isDragOver.value = false;
          workbench.setIsDragging(false);
          if (location.current.dropTargets[0]?.element === self.element) {
            const { key, item } = source.data;
            if (item) {
              schemaStore.setCurrentId(item.id);
              schemaStore.deleteSchema(item.id);
              schemaStore.addSchema(item, props.id);
              schemaStore.setCurrentId(item.id);
            } else {
              const id = generateUUID();
              configStore.addPropsConfig(id, key);
              configStore.addStyleConfig(id, key);
              const json: Widget.RenderConfig = {
                id,
                comp: key,
                props: configStore.getFormatedProps(id),
                style: configStore.getFormatedStyle(id),
              };
              schemaStore.setCurrentId(json.id);
              schemaStore.addSchema(json, props.id);
            }
          }
        },
      });
    });

    watchEffect(() => {
      styled.value = {
        ...props.style,
        // 只在设计模式下显示边框
        ...(workbench.mode === 'design'
          ? {
              border: '1px solid #dedede',
              borderRadius: '4px',
              padding: '4px',
            }
          : {}),
        height: slots.default ? 'auto' : `200px`,
      };
    });
    return { styled, el, attrs, slots, isDragOver };
  },
  render() {
    return (
      <div
        style={this.styled}
        id={this.id}
        ref={d => {
          this.el = d;
        }}
      >
        {this.$slots.default?.()}
        {!this.$slots.default && <KEmpty />}
      </div>
    );
  },
});
