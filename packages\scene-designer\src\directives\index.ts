import type { App } from 'vue';
import permission from './permission';
import resizeCenter from './resizeCenter';
import visible from './visible';

const directiveMap = {
  permission,
  resizeCenter,
  visible,
};

const directives = {
  install: function (app: App) {
    Object.keys(directiveMap).forEach(key => {
      app.directive(key, directiveMap[key as keyof typeof directiveMap]);
    });
  },
};

export default directives;
